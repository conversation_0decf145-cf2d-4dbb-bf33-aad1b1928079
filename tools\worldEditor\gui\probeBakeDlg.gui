//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(ProbeBakeDlg) {
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 2";
   horizSizing = "right";
   vertSizing = "bottom";
   profile = "GuiDefaultProfile";
   visible = "1";
   active = "1";
   tooltipProfile = "GuiToolTipProfile";
   hovertime = "1000";
   isContainer = "1";
   canSave = "1";
   canSaveDynamicFields = "1";

   new GuiWindowCtrl() {
      text = "Update Environment Probes";
      resizeWidth = "0";
      resizeHeight = "0";
      canMove = "1";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      canCollapse = "0";
      closeCommand = "Canvas.popDialog(ProbeBakeDlg);";
      edgeSnap = "0";
      margin = "0 0 0 0";
      padding = "0 0 0 0";
      anchorTop = "1";
      anchorBottom = "0";
      anchorLeft = "1";
      anchorRight = "0";
      position = "392 314";
      extent = "270 164";
      minExtent = "8 2";
      horizSizing = "right";
      vertSizing = "bottom";
      profile = "ToolsGuiWindowProfile";
      visible = "1";
      active = "1";
      tooltipProfile = "GuiToolTipProfile";
      hovertime = "1000";
      isContainer = "1";
      canSave = "1";
      canSaveDynamicFields = "0";

      new GuiTextCtrl() {
         text = "Probe Resolution";
         maxLength = "1024";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         position = "11 32";
         extent = "91 13";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         profile = "ToolsGuiTextProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "GuiToolTipProfile";
         hovertime = "1000";
         isContainer = "1";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
      new GuiPopUpMenuCtrl(ProbeBakeDlg_ProbeResList) {
         maxPopupHeight = "200";
         sbUsesNAColor = "0";
         reverseTextList = "0";
         bitmapBounds = "16 16";
         text = "64";
         maxLength = "1024";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         position = "103 29";
         extent = "157 19";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         profile = "ToolsGuiPopUpMenuProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "GuiToolTipProfile";
         hovertime = "1000";
         isContainer = "1";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
      new GuiTextCtrl() {
         text = "Number of bake iterations";
         maxLength = "1024";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         position = "11 56";
         extent = "129 13";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         profile = "ToolsGuiTextProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "GuiToolTipProfile";
         hovertime = "1000";
         isContainer = "1";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
      new GuiTextEditCtrl(ProbeBakeDlg_NumIterTxt) {
         historySize = "0";
         tabComplete = "0";
         sinkAllKeyEvents = "0";
         password = "0";
         passwordMask = "*";
         text = "1";
         maxLength = "1024";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         position = "150 53";
         extent = "108 18";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         profile = "ToolsGuiTextEditProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "GuiToolTipProfile";
         hovertime = "1000";
         isContainer = "1";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
      new GuiButtonCtrl(ProbeBakeDlg_RunBake) {
         text = "Update Probes";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
         position = "68 120";
         extent = "140 30";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         profile = "ToolsGuiButtonProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "GuiToolTipProfile";
         hovertime = "1000";
         isContainer = "0";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
      new GuiProgressCtrl(ProbeBakeDlg_Progress) {
         maxLength = "1024";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         position = "8 80";
         extent = "251 25";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         profile = "GuiProgressProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "GuiToolTipProfile";
         hovertime = "1000";
         isContainer = "1";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
   };
};
//--- OBJECT WRITE END ---
