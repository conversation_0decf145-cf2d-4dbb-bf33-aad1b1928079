#ifndef JBEAM_PARSER_H
#define JBEAM_PARSER_H

#include <string>
#include <vector>
#include <map>
#include <memory>

// Forward declarations
struct JBeamNode;
struct JBeamBeam;
struct JBeamVehicle;

/**
 * Represents a physics node in the JBEAM system
 * Nodes are the fundamental building blocks of soft-body physics
 */
struct JBeamNode {
    std::string id;           // Node identifier
    float posX, posY, posZ;   // 3D position
    float nodeWeight;         // Mass of the node
    float collision;          // Collision radius
    float selfCollision;      // Self-collision radius
    float frictionCoef;       // Friction coefficient
    bool fixed;               // Whether node is fixed in space
    
    // Default constructor
    JBeamNode() : posX(0), posY(0), posZ(0), nodeWeight(25.0f), 
                  collision(0.5f), selfCollision(0.0f), 
                  frictionCoef(1.0f), fixed(false) {}
};

/**
 * Represents a beam connection between two nodes
 * Beams provide structural integrity and deformation behavior
 */
struct JBeamBeam {
    std::string node1, node2; // Connected node IDs
    float beamSpring;          // Spring constant
    float beamDamp;            // Damping coefficient
    float beamStrength;        // Breaking strength
    float beamDeform;          // Deformation threshold
    bool breakGroup;           // Break group behavior
    
    // Default constructor
    JBeamBeam() : beamSpring(4000000.0f), beamDamp(500.0f),
                  beamStrength(1000000.0f), beamDeform(400000.0f),
                  breakGroup(false) {}
};

/**
 * Represents a complete vehicle definition from JBEAM
 * Contains all nodes, beams, and metadata
 */
struct JBeamVehicle {
    std::string name;
    std::string author;
    std::vector<JBeamNode> nodes;
    std::vector<JBeamBeam> beams;
    std::map<std::string, std::string> information;
    
    // Helper methods
    JBeamNode* findNode(const std::string& id);
    bool validateStructure() const;
};

/**
 * JBEAM file parser class
 * Handles parsing of JSON-based JBEAM files with C-style comments
 */
class JBeamParser {
public:
    JBeamParser();
    ~JBeamParser();
    
    /**
     * Parse a JBEAM file from disk
     * @param filePath Path to the .jbeam file
     * @return Parsed vehicle data or nullptr on failure
     */
    std::unique_ptr<JBeamVehicle> parseFile(const std::string& filePath);
    
    /**
     * Parse JBEAM data from string
     * @param jbeamData Raw JBEAM JSON string
     * @return Parsed vehicle data or nullptr on failure
     */
    std::unique_ptr<JBeamVehicle> parseString(const std::string& jbeamData);
    
    /**
     * Get the last error message
     * @return Error description
     */
    const std::string& getLastError() const { return lastError; }
    
private:
    std::string lastError;
    
    // Internal parsing methods
    std::string removeComments(const std::string& jsonData);
    bool parseNodes(const std::string& jsonData, JBeamVehicle& vehicle);
    bool parseBeams(const std::string& jsonData, JBeamVehicle& vehicle);
    bool parseInformation(const std::string& jsonData, JBeamVehicle& vehicle);
    
    // JSON parsing helpers
    std::string extractJsonValue(const std::string& json, const std::string& key);
    std::vector<std::string> parseJsonArray(const std::string& arrayStr);
    std::map<std::string, std::string> parseJsonObject(const std::string& objStr);
    
    // Utility methods
    std::string trim(const std::string& str);
    bool isValidNodeId(const std::string& id);
    float parseFloat(const std::string& str, float defaultValue = 0.0f);
};

#endif // JBEAM_PARSER_H
