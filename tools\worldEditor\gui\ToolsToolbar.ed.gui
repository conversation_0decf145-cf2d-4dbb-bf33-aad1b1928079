//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiContainer(EWToolsToolbar) {
   canSaveDynamicFields = "0";
   Enabled = "0";
   internalName = "ToolsToolbar";
   isContainer = "1";
   Profile = "ToolsMenubarProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 41";
   Extent = "0 33";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   isClosed = "0";
   isDynamic = "0";
   
   new GuiDynamicCtrlArrayControl(ToolsToolbarArray) {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsMenubarProfile";
      HorizSizing = "width";
      VertSizing = "bottom";
      position = "4 3";
      Extent = "264 32";
      MinExtent = "1024 32";
      canSave = "1";
      Visible = "1";
      hovertime = "1000";
      colCount = "1";
      colSize = "29";
      RowSize = "27";
      rowSpacing = "2";
      colSpacing = "4";
   };
   new GuiBitmapButtonCtrl() {
      canSaveDynamicFields = "0";
      Enabled = "1";
      internalName = "resizeArrow";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "left";
      VertSizing = "bottom";
      position = getWord(EWToolsToolbar.Extent, 0) - 7 SPC "0";
      extent = "7 33";
      MinExtent = "7 2";
      canSave = "1";
      Visible = "1";
      Command = "EWToolsToolbar.ToggleSize();";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Collapse Toolbar";
      hovertime = "750";
      buttonType = "PushButton";
      useMouseEvents = "0";
      bitmapAsset = "ToolsModule:collapse_toolbar_n_image";
   };
   new GuiDecoyCtrl(EWToolsToolbarDecoy) {
      profile = "ToolsGuiDefaultProfile";
      horizSizing = "right";
      vertSizing = "bottom";
      position = "1 1";
      extent = "35 31";
      minExtent = "8 8";
      visible = "0";
      helpTag = "0";
      useMouseEvents = "1";
      isDecoy = "1";
   };
};

