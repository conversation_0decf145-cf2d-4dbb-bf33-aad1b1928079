//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(VerveEditorGui) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "VEditorDefaultProfile";
   HorizSizing = "width";
   VertSizing = "height";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   isDecoy = "0";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";

   new GuiControl() {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "VEditorTransparentProfile";
      HorizSizing = "width";
      VertSizing = "height";
      Position = "0 0";
      Extent = "728 714";
      MinExtent = "8 2";
      canSave = "1";
      isDecoy = "0";
      Visible = "1";
      tooltipprofile = "VEditorDefaultProfile";
      hovertime = "1000";

      new VEditorScrollControl(VerveEditorGroupScroll) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "VEditorScrollProfile";
         HorizSizing = "right";
         VertSizing = "height";
         Position = "1 1";
         Extent = "212 663";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "alwaysOff";
         constantThumbHeight = "0";
         childMargin = "0 0";

         new GuiScriptNotifyCtrl(VerveEditorGroupNotify) {
            notifyOnChildAdded = "0";
            notifyOnChildRemoved = "0";
            notifyOnChildResized = "0";
            notifyOnParentResized = "1";
            notifyOnResize = "1";
            notifyOnLoseFirstResponder = "0";
            notifyOnGainFirstResponder = "0";
            canSaveDynamicFields = "0";
            class = "VerveEditorScrollNotifyV";
            className = "VerveEditorScrollNotifyV";
            Enabled = "1";
            isContainer = "0";
            Profile = "VEditorTransparentProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            Position = "0 0";
            Extent = "212 1";
            MinExtent = "210 1";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            
            new VEditorButton() {
                 class = "VerveEditorTimeLineBackground";
                 canSaveDynamicFields = "1";
                 Enabled = "1";
                 isContainer = "0";
                 Profile = "VEditorTransparentProfile";
                 HorizSizing = "width";
                 VertSizing = "height";
                 position = "0 0";
                 Extent = "212 1";
                 MinExtent = "210 1";
                 canSave = "1";
                 Visible = "1";
                 tooltipprofile = "ToolsGuiToolTipProfile";
                 hovertime = "1000";
                 text = "";
                 groupNum = "-1";
                 buttonType = "PushButton";
                 useMouseEvents = "0";
                 Context = "1";
            };
            new GuiStackControl(VerveEditorGroupStack) {
               class = "VerveEditorStack";
               StackingType = "Vertical";
               HorizStacking = "Left to Right";
               VertStacking = "Top to Bottom";
               Padding = "-1";
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "1";
               Profile = "VEditorTransparentProfile";
               HorizSizing = "width";
               VertSizing = "bottom";
               Position = "0 0";
               Extent = "212 1";
               MinExtent = "210 1";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
            };
         };
      };
      new VEditorScrollControl(VerveEditorTrackScroll) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "VEditorScrollProfile";
         HorizSizing = "width";
         VertSizing = "height";
         Position = "210 1";
         Extent = "516 663";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "alwaysOn";
         constantThumbHeight = "0";
         childMargin = "0 0";

         new GuiScriptNotifyCtrl(VerveEditorTrackNotify) {
            notifyOnChildAdded = "0";
            notifyOnChildRemoved = "0";
            notifyOnChildResized = "0";
            notifyOnParentResized = "1";
            notifyOnResize = "1";
            notifyOnLoseFirstResponder = "0";
            notifyOnGainFirstResponder = "0";
            canSaveDynamicFields = "0";
            class = "VerveEditorScrollNotify";
            className = "VerveEditorScrollNotify";
            Enabled = "1";
            isContainer = "0";
            Profile = "VEditorTransparentProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            Position = "0 0";
            Extent = "516 32";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";

            new VTimeLineControl(VerveEditorTrackTimeLine) {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "1";
               Profile = "VEditorTimeLineProfile";
               HorizSizing = "width";
               VertSizing = "height";
               Position = "0 0";
               Extent = "1100 32";
               MinExtent = "1100 32";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               IsController = "0";
               Controller = "VerveEditorController";
               Zoom = "0";
               
               new VEditorButton() {
                     class = "VerveEditorTimeLineBackground";
                     canSaveDynamicFields = "1";
                     Enabled = "1";
                     isContainer = "0";
                     Profile = "VEditorTransparentProfile";
                     HorizSizing = "width";
                     VertSizing = "height";
                     position = "0 0";
                     Extent = "516 32";
                     MinExtent = "8 8";
                     canSave = "1";
                     Visible = "1";
                     tooltipprofile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     text = "";
                     groupNum = "-1";
                     buttonType = "PushButton";
                     useMouseEvents = "0";
                     Context = "0";
                };
                new GuiStackControl(VerveEditorTrackStack) {
                    class = "VerveEditorStack";
                    StackingType = "Vertical";
                    HorizStacking = "Left to Right";
                    VertStacking = "Top to Bottom";
                    Padding = "-1";
                    canSaveDynamicFields = "0";
                    Enabled = "1";
                    isContainer = "1";
                    Profile = "VEditorTransparentProfile";
                    HorizSizing = "width";
                    VertSizing = "bottom";
                    Position = "0 0";
                    Extent = "516 32";
                    MinExtent = "8 32";
                    canSave = "1";
                    isDecoy = "0";
                    Visible = "1";
                    tooltipprofile = "ToolsGuiToolTipProfile";
                    hovertime = "1000";
                };
            };
         };
      };
      new VEditorScrollControl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "VEditorScrollProfile";
         HorizSizing = "right";
         VertSizing = "top";
         Position = "1 661";
         Extent = "212 56";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiDefaultProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         willFirstRespond = "1";
         hScrollBar = "alwaysOn";
         vScrollBar = "alwaysOff";
         constantThumbHeight = "0";
         childMargin = "1 1";

         new GuiControl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "1";
            Profile = "VEditorTransparentProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            Position = "1 1";
            Extent = "208 36";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";

             new GuiBitmapButtonCtrl(VerveEditorAddGroupButton) {
                canSaveDynamicFields = "0";
                Enabled = "1";
                isContainer = "0";
                Profile = "VEditorTransparentProfile";
                class = "VEditorAddGroupButton";
                HorizSizing = "right";
                VertSizing = "bottom";
                Position = "3 3";
                Extent = "30 30";
                MinExtent = "8 2";
                canSave = "1";
                isDecoy = "0";
                Visible = "1";
                tooltipprofile = "ToolsGuiToolTipProfile";
                tooltip = "Add New Group";
                command = "$ThisControl.DisplayContextMenu();";
                hovertime = "1000";
                groupNum = "-1";
                buttonType = "PushButton";
                useMouseEvents = "0";
                bitmapAsset = "ToolsModule:btn_AddGroup_image";
             };
             new GuiBitmapButtonCtrl(VerveEditorAddTrackButton) {
                canSaveDynamicFields = "0";
                Enabled = "1";
                isContainer = "0";
                Profile = "VEditorTransparentProfile";
                class = "VEditorAddTrackButton";
                HorizSizing = "right";
                VertSizing = "bottom";
                Position = "36 3";
                Extent = "30 30";
                MinExtent = "8 2";
                canSave = "1";
                isDecoy = "0";
                Visible = "1";
                tooltipprofile = "ToolsGuiToolTipProfile";
                tooltip = "Add New Track";
                command = "$ThisControl.DisplayContextMenu();";
                hovertime = "1000";
                groupNum = "-1";
                buttonType = "PushButton";
                useMouseEvents = "0";
                bitmapAsset = "ToolsModule:btn_AddTrack_image";
             };
             new GuiBitmapButtonCtrl(VerveEditorAddEventButton) {
                canSaveDynamicFields = "0";
                Enabled = "1";
                isContainer = "0";
                Profile = "VEditorTransparentProfile";
                HorizSizing = "right";
                VertSizing = "bottom";
                Position = "69 3";
                Extent = "30 30";
                MinExtent = "8 2";
                canSave = "1";
                isDecoy = "0";
                Visible = "1";
                tooltipprofile = "ToolsGuiToolTipProfile";
                tooltip = "Add New Event";
                command = "VerveEditor::AddEvent();";
                hovertime = "1000";
                groupNum = "-1";
                buttonType = "PushButton";
                useMouseEvents = "0";
                bitmapAsset = "ToolsModule:btn_AddEvent_image";
             };
             new GuiBitmapButtonCtrl() {
                canSaveDynamicFields = "0";
                Enabled = "1";
                isContainer = "0";
                Profile = "VEditorBitmapButtonProfile";
                HorizSizing = "left";
                VertSizing = "bottom";
                Position = "175 3";
                Extent = "30 30";
                MinExtent = "8 2";
                canSave = "1";
                isDecoy = "0";
                Visible = "1";
                tooltipprofile = "ToolsGuiToolTipProfile";
                tooltip = "Delete Selected Object(s)";
                command = "VerveEditor::DeleteSelection();";
                hovertime = "1000";
                groupNum = "-1";
                buttonType = "PushButton";
                useMouseEvents = "0";
                bitmapAsset = "ToolsModule:btn_Delete_image";
             };
         };
      };
      new VEditorScrollControl(VerveEditorTimeScroll) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "VEditorScrollProfile";
         HorizSizing = "width";
         VertSizing = "top";
         Position = "210 661";
         Extent = "516 56";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         willFirstRespond = "1";
         hScrollBar = "alwaysOn";
         vScrollBar = "alwaysOn";
         constantThumbHeight = "0";
         childMargin = "0 0";

         new GuiScriptNotifyCtrl(VerveEditorTimeNotify) {
            notifyOnChildAdded = "0";
            notifyOnChildRemoved = "0";
            notifyOnChildResized = "0";
            notifyOnParentResized = "1";
            notifyOnResize = "1";
            notifyOnLoseFirstResponder = "0";
            notifyOnGainFirstResponder = "0";
            canSaveDynamicFields = "0";
            class = "VerveEditorScrollNotifyH";
            className = "VerveEditorScrollNotifyH";
            Enabled = "1";
            isContainer = "0";
            Profile = "VEditorTransparentProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            Position = "1 1";
            Extent = "516 41";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";

            new VTimeLineControl(VerveEditorTimeLine) {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "1";
               Profile = "VEditorTimeLineProfile";
               HorizSizing = "width";
               VertSizing = "bottom";
               Position = "0 0";
               Extent = "1100 41";
               MinExtent = "1100 41";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               IsController = "1";
               Controller = "VerveEditorController";
               Zoom = "0";
            };
         };
      };
   };
   new VEditorScrollControl(VerveEditorPropertyScroll) {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "VEditorScrollProfile";
      HorizSizing = "left";
      VertSizing = "height";
      Position = "723 1";
      Extent = "300 766";
      MinExtent = "8 2";
      canSave = "1";
      isDecoy = "0";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      willFirstRespond = "1";
      hScrollBar = "alwaysOff";
      vScrollBar = "alwaysOn";
      constantThumbHeight = "0";
      childMargin = "3 3";
      
     new GuiStackControl(VerveEditorPropertyStack) {
        StackingType = "Vertical";
        HorizStacking = "Left to Right";
        VertStacking = "Top to Bottom";
        Padding = "2";
        canSaveDynamicFields = "0";
        Enabled = "1";
        isContainer = "1";
        Profile = "VEditorTransparentProfile";
        HorizSizing = "width";
        VertSizing = "bottom";
        Position = "3 3";
        Extent = "279 256";
        MinExtent = "16 16";
        canSave = "1";
        isDecoy = "0";
        Visible = "1";
        tooltipprofile = "ToolsGuiToolTipProfile";
        hovertime = "1000";
     };
   };
   new VEditorScrollControl() {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "VEditorScrollProfile";
      HorizSizing = "right";
      VertSizing = "top";
      Position = "1 713";
      Extent = "212 54";
      MinExtent = "8 2";
      canSave = "1";
      isDecoy = "0";
      Visible = "1";
      tooltipprofile = "ToolsGuiDefaultProfile";
      hovertime = "1000";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      willFirstRespond = "1";
      hScrollBar = "alwaysOff";
      vScrollBar = "alwaysOff";
      constantThumbHeight = "0";
      childMargin = "1 1";

      new GuiControl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "VEditorTransparentProfile";
         HorizSizing = "width";
         VertSizing = "bottom";
         Position = "1 1";
         Extent = "208 36";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
      };
   };
   new VEditorScrollControl() {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "VEditorScrollProfile";
      HorizSizing = "width";
      VertSizing = "top";
      Position = "210 713";
      Extent = "516 54";
      MinExtent = "8 2";
      canSave = "1";
      isDecoy = "0";
      Visible = "1";
      tooltipprofile = "ToolsGuiDefaultProfile";
      hovertime = "1000";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      willFirstRespond = "1";
      hScrollBar = "alwaysOff";
      vScrollBar = "alwaysOn";
      constantThumbHeight = "0";
      childMargin = "1 1";

      new GuiControl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "VEditorTransparentProfile";
         HorizSizing = "width";
         VertSizing = "height";
         Position = "2 2";
         Extent = "500 50";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";

         new GuiControl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "1";
            Profile = "VEditorTransparentProfile";
            HorizSizing = "center";
            VertSizing = "bottom";
            Position = "114 10";
            Extent = "258 30";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";

            new GuiBitmapButtonCtrl() {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "0";
               Profile = "VEditorBitmapButtonProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               Position = "0 0";
               Extent = "50 30";
               MinExtent = "8 2";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               tooltip = "Jump Backwards";
               command = "VerveEditor::Rewind();";
               hovertime = "1000";
               groupNum = "-1";
               buttonType = "PushButton";
               useMouseEvents = "0";
               bitmapAsset = "ToolsModule:btn_Rewind_image";
            };
            new GuiBitmapButtonCtrl() {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "0";
               Profile = "VEditorTransparentProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               Position = "52 0";
               Extent = "50 30";
               MinExtent = "8 2";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               tooltip = "Step Backwards 1 Frame";
               command = "VerveEditor::StepB();";
               hovertime = "1000";
               groupNum = "-1";
               buttonType = "PushButton";
               useMouseEvents = "0";
               bitmapAsset = "ToolsModule:btn_StepB_image";
            };
            new GuiBitmapButtonCtrl(VerveEditorPlayButton) {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "0";
               Profile = "VEditorTransparentProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               Position = "104 0";
               Extent = "50 30";
               MinExtent = "8 2";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               tooltip = "Play / Pause";
               command = "VerveEditor::TogglePlay( $ThisControl );";
               hovertime = "1000";
               groupNum = "-1";
               buttonType = "PushButton";
               useMouseEvents = "0";
               bitmapAsset = "ToolsModule:btn_Play_image";
            };
            new GuiBitmapButtonCtrl() {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "0";
               Profile = "VEditorTransparentProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               Position = "156 0";
               Extent = "50 30";
               MinExtent = "8 2";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               tooltip = "Step Forward 1 Frame";
               command = "VerveEditor::StepF();";
               hovertime = "1000";
               groupNum = "-1";
               buttonType = "PushButton";
               useMouseEvents = "0";
               bitmapAsset = "ToolsModule:btn_StepF_image";
            };
            new GuiBitmapButtonCtrl() {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "0";
               Profile = "VEditorBitmapButtonProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               Position = "208 0";
               Extent = "50 30";
               MinExtent = "8 2";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               tooltip = "Jump Forward";
               command = "VerveEditor::Forward();";
               hovertime = "1000";
               groupNum = "-1";
               buttonType = "PushButton";
               useMouseEvents = "0";
               bitmapAsset = "ToolsModule:btn_Forward_image";
            };
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "VEditorTransparentProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "10 10";
            Extent = "30 30";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Insert Time (Front)";
            command = "VerveEditor::InsertTimeFront();";
            hovertime = "1000";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:btn_AddL_image";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "VEditorTransparentProfile";
            HorizSizing = "left";
            VertSizing = "bottom";
            Position = "460 10";
            Extent = "30 30";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Insert Time (Back)";
            command = "VerveEditor::InsertTimeBack();";
            hovertime = "1000";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:btn_AddR_image";
         };
      };
   };
};
//--- OBJECT WRITE END ---
