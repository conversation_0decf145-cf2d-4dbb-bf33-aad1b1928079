//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(AxisGizmoSettingsTab,EditorGuiGroup) {
   isContainer = "1";
   profile = "ToolsGuiDefaultProfile";
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 2";
   visible = "1";
   active = "1";
   tooltipProfile = "ToolsGuiToolTipProfile";
   hovertime = "1000";
   canSave = "1";
   canSaveDynamicFields = "1";

   new GuiTabPageCtrl(EAxisGizmoSettingsPage) {
      fitBook = "1";
      text = "Axis Gizmo";
      maxLength = "1024";
      margin = "0 0 0 0";
      padding = "0 0 0 0";
      anchorTop = "1";
      anchorBottom = "0";
      anchorLeft = "1";
      anchorRight = "0";
      isContainer = "1";
      profile = "ToolsGuiSolidDefaultProfile";
      horizSizing = "width";
      vertSizing = "height";
      position = "0 0";
      extent = "208 568";
      minExtent = "8 2";
      visible = "1";
      active = "1";
      tooltipProfile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      canSave = "1";
      canSaveDynamicFields = "1";

      new GuiScrollCtrl() {
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "1";
         lockVertScroll = "0";
         constantThumbHeight = "0";
         childMargin = "0 0";
         mouseWheelScrollSpeed = "-1";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         isContainer = "1";
         profile = "ToolsGuiScrollProfile";
         horizSizing = "width";
         vertSizing = "height";
         position = "0 0";
         extent = "208 568";
         minExtent = "8 2";
         visible = "1";
         active = "1";
         tooltipProfile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSave = "1";
         canSaveDynamicFields = "0";
         
         new GuiStackControl() {
            StackingType = "Vertical";
            HorizStacking = "Left to Right";
            VertStacking = "Top to Bottom";
            Padding = "0";
            isContainer = "1";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            position = "1 1";
            extent = "208 210";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            
            new GuiRolloutCtrl() {
               Profile = "GuiRolloutProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               position = "10 10";
               extent = "208 95";
               Caption = "Gizmo";
               Margin = "0 0 0 -3";
               DragSizable = false;
               container = true;
                
               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  Padding = "0";
                  isContainer = "1";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "0 0";
                  Extent = "208 0";
                  MinExtent = "8 2";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000"; 
            
                  new GuiContainer(){
                     HorizSizing = "right";
                     VertSizing = "bottom";
                     Position = "-1 0";
                     Extent = "208 79";
                     Docking = "none";
                     
                     new GuiTextCtrl() {
                        text = "Rotate Scalar:";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextRightProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "5 6";
                        extent = "70 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "0";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        text = "0.8";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiNumericTextEditProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "81 5";
                        extent = "121 18";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowTextEdit";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "AxisGizmo/mouseRotateScalar";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                     new GuiTextCtrl() {
                        text = "Scale Scalar:";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextRightProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "5 26";
                        extent = "70 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "0";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        text = "0.8";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiNumericTextEditProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "81 24";
                        extent = "121 17";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowTextEdit";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "AxisGizmo/mouseScaleScalar";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                     new GuiCheckBoxCtrl() {
                        useInactiveState = "0";
                        text = "Render When Manipulated";
                        groupNum = "-1";
                        buttonType = "ToggleButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiCheckBoxProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "5 44";
                        extent = "140 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowCheckbox";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "AxisGizmo/renderWhenUsed";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                     new GuiCheckBoxCtrl() {
                        useInactiveState = "0";
                        text = "Render Tool Text";
                        groupNum = "-1";
                        buttonType = "ToggleButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiCheckBoxProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "5 61";
                        extent = "140 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowCheckbox";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "AxisGizmo/renderInfoText";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                  };
               };
            };
            new GuiRolloutCtrl() {
               Profile = "GuiRolloutProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               position = "10 10";
               extent = "208 95";
               Caption = "Grid";
               Margin = "0 0 0 -3";
               DragSizable = false;
               container = true;
                
               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  Padding = "0";
                  isContainer = "1";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "0 0";
                  Extent = "208 0";
                  MinExtent = "8 2";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000"; 
            
                  new GuiContainer(){
                     HorizSizing = "right";
                     VertSizing = "bottom";
                     Position = "-1 0";
                     Extent = "208 82";
                     Docking = "none";

                     new GuiCheckBoxCtrl() {
                        useInactiveState = "0";
                        text = "Render Plane";
                        groupNum = "-1";
                        buttonType = "ToggleButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiCheckBoxProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "5 4";
                        extent = "140 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowCheckbox";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "AxisGizmo/Grid/renderPlane";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                     new GuiCheckBoxCtrl() {
                        useInactiveState = "0";
                        text = "Render Plane Hashes";
                        groupNum = "-1";
                        buttonType = "ToggleButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiCheckBoxProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "5 21";
                        extent = "140 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowCheckbox";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "AxisGizmo/Grid/renderPlaneHashes";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                     new GuiTextCtrl() {
                        text = "Plane Size:";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextRightProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "5 40";
                        extent = "70 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "0";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        text = "500";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiNumericTextEditProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "81 38";
                        extent = "121 17";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowTextEdit";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "AxisGizmo/Grid/planeDim";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                     new GuiControl() {
                        isContainer = "1";
                        profile = "ToolsGuiDefaultProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "5 58";
                        extent = "208 18";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColor";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "AxisGizmo/Grid/gridColor";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";

                        new GuiTextCtrl() {
                           text = "Plane Color:";
                           maxLength = "1024";
                           margin = "0 0 0 0";
                           padding = "0 0 0 0";
                           anchorTop = "1";
                           anchorBottom = "0";
                           anchorLeft = "1";
                           anchorRight = "0";
                           isContainer = "0";
                           profile = "ToolsGuiTextRightProfile";
                           horizSizing = "right";
                           vertSizing = "bottom";
                           position = "0 2";
                           extent = "70 14";
                           minExtent = "8 2";
                           visible = "1";
                           active = "1";
                           tooltipProfile = "ToolsGuiToolTipProfile";
                           hovertime = "1000";
                           canSave = "1";
                           canSaveDynamicFields = "1";
                        };
                        new GuiTextEditCtrl() {
                           historySize = "0";
                           password = "0";
                           tabComplete = "0";
                           sinkAllKeyEvents = "0";
                           passwordMask = "*";
                           maxLength = "1024";
                           margin = "0 0 0 0";
                           padding = "0 0 0 0";
                           anchorTop = "1";
                           anchorBottom = "0";
                           anchorLeft = "1";
                           anchorRight = "0";
                           isContainer = "0";
                           profile = "ToolsGuiTextEditProfile";
                           horizSizing = "width";
                           vertSizing = "bottom";
                           position = "76 0";
                           extent = "104 18";
                           minExtent = "8 2";
                           visible = "1";
                           active = "1";
                           tooltipProfile = "ToolsGuiToolTipProfile";
                           hovertime = "1000";
                           internalName = "ColorEdit";
                           canSave = "1";
                           canSaveDynamicFields = "1";
                           class = "ESettingsWindowColorEdit";
                        };
                        new GuiSwatchButtonCtrl() {
                           color = "0 0 0 0";
                           groupNum = "-1";
                           buttonType = "PushButton";
                           useMouseEvents = "0";
                           isContainer = "0";
                           profile = "ToolsGuiDefaultProfile";
                           horizSizing = "left";
                           vertSizing = "bottom";
                           position = "184 2";
                           extent = "14 14";
                           minExtent = "8 2";
                           visible = "1";
                           active = "1";
                           tooltipProfile = "ToolsGuiToolTipProfile";
                           hovertime = "1000";
                           internalName = "ColorButton";
                           canSave = "1";
                           canSaveDynamicFields = "1";
                           class = "ESettingsWindowColorButton";
                        };
                     };
                  };
               };
            };
         };
      };
   };
};
//--- OBJECT WRITE END ---
