//-----------------------------------------------------------------------------
// Ground Material Definition
// Basic material for the test track ground
//-----------------------------------------------------------------------------

singleton Material(GroundMaterial) {
    mapTo = "ground";
    
    // Diffuse properties
    diffuseMap[0] = "beamng_replica/assets/materials/ground_diffuse.png";
    normalMap[0] = "beamng_replica/assets/materials/ground_normal.png";
    
    // Material properties
    specular[0] = "0.1 0.1 0.1 1.0";
    specularPower[0] = 32.0;
    
    // Surface properties
    friction = 0.8;
    restitution = 0.2;
    
    // Rendering properties
    translucent = false;
    alphaTest = false;
    alphaRef = 0;
    
    // Texture coordinates
    diffuseMap[0] = "core/art/terrains/grassland/grass1.jpg";
    
    // Tiling
    materialTag0 = "Terrain";
};

singleton Material(AsphaltMaterial) {
    mapTo = "asphalt";
    
    // Use a basic texture for now
    diffuseMap[0] = "core/art/terrains/grassland/rock1.jpg";
    
    // Material properties for road surface
    specular[0] = "0.2 0.2 0.2 1.0";
    specularPower[0] = 64.0;
    
    // High friction for good grip
    friction = 1.0;
    restitution = 0.1;
    
    materialTag0 = "Road";
};

singleton Material(VehicleMaterial) {
    mapTo = "vehicle_body";
    
    // Basic metallic appearance
    diffuseMap[0] = "core/art/shapes/sphere_blue.jpg";
    
    // Metallic properties
    specular[0] = "0.8 0.8 0.8 1.0";
    specularPower[0] = 128.0;
    
    // Vehicle surface properties
    friction = 0.6;
    restitution = 0.3;
    
    materialTag0 = "Vehicle";
};
