//-----------------------------------------------------------------------------
// Verve
// Copyright (C) - Violent Tulip
//-----------------------------------------------------------------------------

function VerveEditor::CreateVCommandEnumField( %fieldContainer, %fieldName )
{
    // Create Enum Menu.
    %fieldInput = VerveEditor::CreateEnumField( %fieldContainer, %fieldName, "VEditorVCommandEnumPropertyField" );
    
    // Populate Menu.
    %fieldInput.add( "EXPRESSION", 0 );
    %fieldInput.add( "METHOD",     1 );
    
    return %fieldInput;
}