//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(EWTerrainEditToolbar,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   internalName = "TerrainEditToolbar";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "306 0";
   Extent = "800 40";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   
   new GuiTextCtrl() {
      profile = "ToolsGuiTextProfile";
      horizSizing = "right";
      vertSizing = "bottom";
      position = "6 7";
      extent = "70 16";
      minExtent = "8 8";
      visible = "1";
      text = "Brush Settings";
      maxLength = "255";
      helpTag = "0";
   };

   new GuiControl() {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiDefaultProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "760 40";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      hovertime = "1000";
      
      new GuiControl(EWTerrainEditToolbarBrushType){
         isContainer = "1";
         profile = "ToolsGuiDefaultProfile";
         Position = "83 2";
         Extent = "94 27";
         
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "ellipse";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "0 0";
            Extent = "29 27";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "ETerrainEditor.toggleBrushType($ThisControl);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Circle Brush (V)";
            hovertime = "750";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:circleBrush_n_image";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "box";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "31 0";
            Extent = "29 27";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "ETerrainEditor.toggleBrushType($ThisControl);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Box Brush (B)";
            hovertime = "750";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:boxBrush_n_image";
         };
         
         /*
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "selection";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "62 0";
            Extent = "29 27";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "ETerrainEditor.toggleBrushType($ThisControl);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Toggles the brush type.";
            hovertime = "750";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:maskBrush_n_image";
         };
         */
      };
      
  new GuiBitmapCtrl() {
      Enabled = "1";
      Profile = "ToolsGuiDefaultProfile";
      position = "152 3";
      Extent = "2 26";
      MinExtent = "1 1";
      bitmapAsset = "ToolsModule:separator_h_image";
   };
     
      new GuiControl(TerrainBrushSizeTextEditContainer) {
         canSaveDynamicFields = "0";
         isContainer = "1";
         Profile = "ToolsGuiTransparentProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "145 5";
         Extent = "120 50";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";

         new GuiTextCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "21 5";
            Extent = "47 10";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "Size";
            maxLength = "1024";
         };
         new GuiTextEditCtrl() {
            canSaveDynamicFields = "0";
            internalName = "textEdit";
            isContainer = "0";
            HorizSizing = "right";
            profile="ToolsGuiNumericDropSliderTextProfile";
            VertSizing = "bottom";
            position = "49 2";
            Extent = "42 16";
            MinExtent = "8 16";
            canSave = "1";
            Visible = "1";
            Command = "ETerrainEditor.setBrushSize( $ThisControl.getText() );";
            validate = "TerrainEditorPlugin.validateBrushSize();";
            hovertime = "1000";
            text = "9";
            maxLength = "4";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "83 2";
            Extent = "18 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "Canvas.pushDialog(TerrainBrushSizeSliderCtrlContainer);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Changes size of the brush (CTRL + Mouse Wheel)";
            hovertime = "750";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:dropslider_n_image";
         };
      };
      new GuiBitmapCtrl() {
      Enabled = "1";
      Profile = "ToolsGuiDefaultProfile";
      position = "272 3";
      Extent = "2 26";
      MinExtent = "1 1";
      bitmapAsset = "ToolsModule:separator_h_image";
      };
            
      new GuiControl(TerrainBrushPressureTextEditContainer) {
         canSaveDynamicFields = "0";
         isContainer = "1";
         Profile = "ToolsGuiTransparentProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "287 5";
         Extent = "120 50";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";

         new GuiTextCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "0 5";
            Extent = "47 10";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "Pressure";
            maxLength = "1024";
         };
         new GuiTextEditCtrl() {
            canSaveDynamicFields = "0";
            internalName = "textEdit";
            isContainer = "0";
            profile="ToolsGuiNumericDropSliderTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "49 2";
            Extent = "42 16";
            MinExtent = "8 16";
            canSave = "1";
            Visible = "1";
            Command = "ETerrainEditor.setBrushPressure( ($ThisControl.getValue() / 100) );";
            hovertime = "1000";
            text = "100";
            maxLength = "3";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "83 2";
            Extent = "18 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "Canvas.pushDialog(TerrainBrushPressureSliderCtrlContainer);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Changes the pressure (CTRL + SHIFT + Mouse Wheel)";
            hovertime = "750";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:dropslider_n_image";
         };
      };
      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         position = "412 3";
         Extent = "2 26";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };

      new GuiControl(TerrainBrushSoftnessTextEditContainer) {
         canSaveDynamicFields = "0";
         isContainer = "1";
         Profile = "ToolsGuiTransparentProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "429 5";
         Extent = "120 50";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";

         new GuiTextCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "0 5";
            Extent = "47 10";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "Softness";
            maxLength = "1024";
         };
         new GuiTextEditCtrl() {
            canSaveDynamicFields = "0";
            internalName = "textEdit";
            isContainer = "0";
            HorizSizing = "right";
            profile="ToolsGuiNumericDropSliderTextProfile";
            VertSizing = "bottom";
            position = "49 2";
            Extent = "42 16";
            MinExtent = "8 16";
            canSave = "1";
            Visible = "1";
            Command = "ETerrainEditor.setBrushSoftness( ($ThisControl.getValue() / 100) );";
            hovertime = "1000";
            text = "1";
            maxLength = "3";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "83 2";
            Extent = "18 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "Canvas.pushDialog(TerrainBrushSoftnessSliderCtrlContainer);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Changes the softness (SHIFT + Mouse Wheel)";
            hovertime = "750";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:dropslider_n_image";
         };
      };

      new GuiBitmapButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "547 3";
         Extent = "29 27";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "Canvas.pushDialog( TerrainBrushSoftnessCurveDlg );";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Changes the softness curve";
         hovertime = "750";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
         bitmapAsset = "ToolsModule:softCurve_n_image";
      };

      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         position = "589 3";
         Extent = "2 26";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };

      new GuiControl(TerrainSetHeightTextEditContainer) {
         canSaveDynamicFields = "0";
         isContainer = "1";
         Profile = "ToolsGuiTransparentProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "605 5";
         Extent = "120 50";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";

         new GuiTextCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "0 5";
            Extent = "33 10";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "Height";
            maxLength = "1024";
         };
         new GuiTextEditCtrl() {
            canSaveDynamicFields = "0";
            internalName = "textEdit";
            isContainer = "0";
            HorizSizing = "right";
            profile="ToolsGuiNumericDropSliderTextProfile";
            VertSizing = "bottom";
            position = "34 2";
            Extent = "62 16";
            MinExtent = "8 16";
            canSave = "1";
            Visible = "1";
            Command = "ETerrainEditor.setHeightVal = $ThisControl.getValue();";
            hovertime = "1000";
            text = "1";
            maxLength = "7";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "88 2";
            Extent = "18 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "Canvas.pushDialog(TerrainSetHeightSliderCtrlContainer);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Changes the height for the SetHeight tool (ALT + Left Mouse)";
            hovertime = "750";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:dropslider_n_image";
         };
      };
   };
};

new GuiMouseEventCtrl(TerrainBrushSizeSliderCtrlContainer,EditorGuiGroup) {
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 8";
   visible = "1";
   helpTag = "0";
   class = "EditorDropdownSliderContainer";
   
   new GuiSliderCtrl() {
      canSaveDynamicFields = "0";
      internalName = "slider";
      isContainer = "0";
      Profile = "ToolsGuiSliderBoxProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = TerrainBrushSizeTextEditContainer.position.x + EWTerrainEditToolbar.position.x + 50 SPC 
         TerrainBrushSizeTextEditContainer.position.y + 50;
      Extent = "112 20";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      AltCommand = "TerrainBrushSizeTextEditContainer-->textEdit.setValue(mCeil($ThisControl.getValue())); ETerrainEditor.setBrushSize( $ThisControl.value );";
      range = "1 256";
      ticks = "0";
      value = "0";
   };
};

new GuiMouseEventCtrl(TerrainBrushPressureSliderCtrlContainer,EditorGuiGroup) {
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 8";
   visible = "1";
   helpTag = "0";
   class = "EditorDropdownSliderContainer";
   
   new GuiSliderCtrl() {
      canSaveDynamicFields = "0";
      internalName = "slider";
      isContainer = "0";
      Profile = "ToolsGuiSliderBoxProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = TerrainBrushPressureTextEditContainer.position.x + EWTerrainEditToolbar.position.x + 50 SPC 
         TerrainBrushPressureTextEditContainer.position.y + 50;
      Extent = "112 20";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      AltCommand = "TerrainBrushPressureTextEditContainer-->textEdit.setValue( mCeil(100 * $ThisControl.getValue()) @ \"%\"); ETerrainEditor.setBrushPressure( $ThisControl.value );";
      range = "0.01 1";
      ticks = "0";
      value = "0";
   };
};
         
new GuiMouseEventCtrl(TerrainBrushSoftnessSliderCtrlContainer,EditorGuiGroup) {
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 8";
   visible = "1";
   helpTag = "0";
   class = "EditorDropdownSliderContainer";
   
   new GuiSliderCtrl() {
      canSaveDynamicFields = "0";
      internalName = "slider";
      isContainer = "0";
      Profile = "ToolsGuiSliderBoxProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = TerrainBrushSoftnessTextEditContainer.position.x + EWTerrainEditToolbar.position.x + 50 SPC 
         TerrainBrushSoftnessTextEditContainer.position.y + 50;
      Extent = "112 20";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      AltCommand = "TerrainBrushSoftnessTextEditContainer-->textEdit.setValue( mCeil(100 * $ThisControl.getValue()) @ \"%\"); ETerrainEditor.setBrushSoftness( $ThisControl.value );";
      range = "0 1";
      ticks = "0";
      value = "0";
   };
};

new GuiMouseEventCtrl(TerrainSetHeightSliderCtrlContainer,EditorGuiGroup) {
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 8";
   visible = "1";
   helpTag = "0";
   class = "EditorDropdownSliderContainer";

   new GuiSliderCtrl() {
      canSaveDynamicFields = "0";
      internalName = "slider";
      isContainer = "0";
      Profile = "ToolsGuiSliderBoxProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = TerrainSetHeightTextEditContainer.position.x + EWTerrainEditToolbar.position.x SPC 
         TerrainSetHeightTextEditContainer.position.y + 50;
      Extent = "112 20";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      AltCommand = "TerrainSetHeightTextEditContainer-->textEdit.setValue( $ThisControl.getValue() ); ETerrainEditor.setHeightVal = $ThisControl.getValue();";
      range = "0 2047";
      ticks = "0";
      value = "100";
   };
};
//--- OBJECT WRITE END ---
