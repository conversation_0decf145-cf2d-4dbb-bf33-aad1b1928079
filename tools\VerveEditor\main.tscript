//-----------------------------------------------------------------------------
// Verve
// Copyright (C) - Violent Tulip
//-----------------------------------------------------------------------------

function InitializeVerveEditor()
{
   $Verve::UseSeparateWindow = ($pref::Video::deviceMode == 0); //can't do separate window atm if you're in fullscreen or borderless full
   
    // Preferences.
    exec( "./DefaultPrefs." @ $TorqueScriptFileExtension );
    
    // GUI.
    exec( "./GUI/GuiProfiles." @ $TorqueScriptFileExtension );
    exec( "./GUI/VerveEditorGroupBuilder.gui" );
    exec( "./GUI/VerveEditorImportPathNodes.gui" );
    
    // Scripts.
    exec( "./Scripts/Plugin." @ $TorqueScriptFileExtension );
    exec( "./Scripts/Utility." @ $TorqueScriptFileExtension );
    
    exec( "./Scripts/EditorControls." @ $TorqueScriptFileExtension );
    exec( "./Scripts/EditorHistory." @ $TorqueScriptFileExtension );
    exec( "./Scripts/EditorMenu." @ $TorqueScriptFileExtension );
    exec( "./Scripts/EditorPreferences." @ $TorqueScriptFileExtension );
    exec( "./Scripts/EditorWindow." @ $TorqueScriptFileExtension );
    exec( "./Scripts/Persistence." @ $TorqueScriptFileExtension );
    exec( "./Scripts/ScrollNotify." @ $TorqueScriptFileExtension );
    exec( "./Scripts/VObject." @ $TorqueScriptFileExtension );
    
    exec( "./Scripts/Inspector/main." @ $TorqueScriptFileExtension );
    
    exec( "./Scripts/Controller/main." @ $TorqueScriptFileExtension );
    exec( "./Scripts/Groups/main." @ $TorqueScriptFileExtension );
    exec( "./Scripts/Tracks/main." @ $TorqueScriptFileExtension );
    exec( "./Scripts/Events/main." @ $TorqueScriptFileExtension );
    
    exec( "./Torque/main." @ $TorqueScriptFileExtension );
    
    // Register Events.
    VerveEditor::RegisterEvent( "VGroupObjectUpdate" );
}

function DestroyVerveEditor()
{
    // Ensure the Editor has Shutdown.
    if ( isObject( VerveEditorWindow ) )
    {
        // Prompt for Save.
        VerveEditor::SavePrompt();
        
        // Reset.
        VerveEditor::Reset();
        
        // Delete the Window.
        VerveEditorWindow.delete();
    }
}

function ToggleVerveEditor( %value )
{
    if ( %value)
    {
        if ( !isObject( VerveEditorWindow ) )
        {
            VerveEditor::LaunchEditor();
        }
        else
        {
            VerveEditorWindow.onWindowClose();
        }
    }
}

function VerveEditor::LaunchEditor()
{
    // Launch Window.
    %mainScreen = VerveEditorWindow::Open();
    
    if ( !isObject( VerveEditorGui ) )
    {
        // Load the GUI.
        exec ( "./GUI/VerveEditor.gui" );
    }
    
    // Apply GUI.
    if($Verve::UseSeparateWindow)
      %mainScreen.setContent( VerveEditorGUI );
   else
   {
      %mainScreen.add(VerveEditorGUI);
      VerveEditorGUI.position.y = VerveEditorGUI.Position.y + 20;
      EditorGUI.add(%mainScreen);
   }
    
    // Clear History.
    VerveEditor::ClearHistory();
    
    // Update Window Title.
      VerveEditorWindow.UpdateWindowTitle();
    
    // Update Selection.
    VerveEditor::OnSelectionUpdate();
    
    // Update Sizes.
    VerveEditor::UpdateSizes();
}

function VerveEditor::ResetController()
{
    // Delete.
    VerveEditor::DeleteController();
    // Create.
    return VerveEditor::CreateController();
}

function VerveEditor::DeleteController()
{
    // Current Controller?
    if ( isObject( $VerveEditor::Controller ) )
    {
        // Stop but do not Reset.
        $VerveEditor::Controller.stop( false );
        // Delete the Controller.
        $VerveEditor::Controller.delete();
        // Deleted?
        return !isObject( $VerveEditor::Controller );
    }
    
    // No Deletion.
    return false;
}

function VerveEditor::CreateController()
{
    // Current Controller?
    if ( !isObject( VerveEditorController ) )
    {
        // Create Controller.
        $VerveEditor::Controller = new VController( VerveEditorController );
    }

    // Return ID.
    return $VerveEditor::Controller;
}

function VerveEditor::Refresh()
{
    if ( !isObject( $VerveEditor::Controller ) )
    {
        return;
    }
    
    // Clear Selection.
    VerveEditor::ClearSelection();
    
    // Delete Existing Controls.
    VerveEditor::DeleteControls();
    
    // Sort Groups & Tracks.
    $VerveEditor::Controller.sortGroups();
    $VerveEditor::Controller.sortTracks();
    
    %groupSet   = $VerveEditor::Controller;
    %groupCount = %groupSet.getCount();
    for ( %i = 0; %i < %groupCount; %i++ )
    {
        // Update Controls.
        %groupSet.getObject( %i ).Refresh();
    }
    
    // Update Window Title.
    VerveEditorWindow.UpdateWindowTitle();
    
    // Update Duration.
    VerveEditor::UpdateDuration();
    
    // Update Sizes.
    VerveEditor::UpdateSizes();
    
    // Update Selection.
    VerveEditor::OnSelectionUpdate();
}

function VerveEditor::UpdateSizes()
{
    VerveEditorGroupNotify.UpdateSize();
    VerveEditorTrackNotify.UpdateSize();
    VerveEditorTimeNotify.UpdateSize();
}

function VerveEditor::UpdateDuration( %duration )
{
    if ( %duration !$= "" )
    {
        // Update Duration.
        $VerveEditor::Controller.setFieldValue( "Duration", %duration );
    }
    
    // Update Duration.
    VerveEditorTimeLine.updateDuration();
    VerveEditorTrackTimeLine.updateDuration();
    
    // Update Sizes.
    VerveEditorGroupNotify.UpdateSize();
    VerveEditorTrackNotify.UpdateSize();
    VerveEditorTimeNotify.UpdateSize();
}

package VerveEditorSaveIntercept
{
    function EditorSaveMission()
    {
        // Reset.
        VerveEditor::Reset();
        
        // Perform the Save.
        Parent::EditorSaveMission();
    }
};

function VerveEditor::Reset()
{
    // Valid Controller?
    if ( isObject( $VerveEditor::Controller ) )
    {
        // Reset.
        $VerveEditor::Controller.Reset();
        
        // Stop.
        $VerveEditor::Controller.Stop();
    }
}
