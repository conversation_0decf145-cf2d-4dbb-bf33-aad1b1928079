//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(EWorldEditorToolbar, EditorGuiGroup) {
   canSaveDynamicFields = "0";
   internalName = "WorldEditorToolbar";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsMenubarProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "306 0";
   Extent = "600" SPC getWord(EditorGuiToolbar.extent, 1);
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   
   new GuiStackControl() {
      StackingType = "Horizontal";
      HorizStacking = "Left to Right";
      VertStacking = "Top to Bottom";
      Padding = "0";
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsMenubarProfile";
      HorizSizing = "width";
      VertSizing = "bottom";
      Position = "0 3";
      Extent = "190 31";
      MinExtent = "16 16";
      canSave = "1";
      isDecoy = "0";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      changeChildSizeToFit = false;
      padding = "2";
                  
      new GuiBitmapButtonCtrl(FitToSelectionBtn) {
         canSaveDynamicFields = "0";
         internalName = "";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "2 3";
         Extent = "29 27";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Command = "commandToServer('EditorCameraAutoFit', EWorldEditor.getSelectionRadius()+1);";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Fit View To Selection (F)";
         hovertime = "1000";
         bitmapAsset = "ToolsModule:fit_selection_n_image";
         text = "";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      
      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         position = "34 3";
         Extent = "2 26";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };
      
      new GuiTextCtrl() {
         profile = "ToolsGuiTextProfile";
         horizSizing = "right";
         vertSizing = "bottom";
         position = "37 7";
         extent = "77 16";
         minExtent = "8 8";
         visible = "1";
         text = " World Settings";
         maxLength = "255";
         helpTag = "0";
      };
      
      new GuiControl(SnapToBar){
         isContainer = "1";
         profile = "ToolsMenubarProfile";
         Position = "116 3";
         Extent = "123 27";
         Padding = "4";
         
         new GuiBitmapButtonCtrl() {
         canSaveDynamicFields = "0";
         internalName = "snappingSettingsBtn";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "0 0";
         Extent = "29 27";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Command ="ESnapOptions.ToggleVisibility();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Snapping Options";
         hovertime = "1000";
         groupNum = "-1";
         buttonType = "ToggleButton";
         useMouseEvents = "0";
         buttonMargin = "0 0";
         bitmapAsset = "ToolsModule:snapping_settings_n_image";
         
         new GuiBitmapCtrl(){
            HorizSizing = "left";
            VertSizing = "top";
            Position = "23 21";
            Extent = "4 4";
            MinExtent = "4 4";
            bitmapAsset = "ToolsModule:dropdown_button_arrow_image";
         };
      }; 
         
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "objectGridSnapBtn";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "31 0";
            Extent = "29 27";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "toggleSnappingOptions(\"grid\");";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Toggles grid snapping (G)";
            hovertime = "1000";
            groupNum = "-1";
            buttonType = "toggleButton";
            useMouseEvents = "0";
            groupNum = "-1";
            bitmapAsset = "ToolsModule:menubar_snap_grid_n_image";
            textMargin = "4";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "objectSnapDownBtn";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "62 0";
            Extent = "29 27";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "toggleSnappingOptions(\"terrain\");";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "All objects will snap to the terrain (T)";
            hovertime = "1000";
            groupNum = "-1";
            buttonType = "toggleButton";
            useMouseEvents = "0";
            groupNum = "-1";
            bitmapAsset = "ToolsModule:snap_terrain_n_image";
            textMargin = "4";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "objectSnapBtn";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "93 0";
            Extent = "29 27";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "toggleSnappingOptions(\"soft\");";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Soft object snapping to other objects (B)";
            hovertime = "1000";
            groupNum = "-1";
            buttonType = "toggleButton";
            useMouseEvents = "0";
            groupNum = "-1";
            bitmapAsset = "ToolsModule:snap_objects_n_image";
            textMargin = "4";
         };
      };
      new GuiControl() {
         canSaveDynamicFields = "0";
         internalName = "softSnapSizeTextEditContainer";
         isContainer = "1";
         Profile = "ToolsGuiTransparentProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "178 5";
         Extent = "56 22";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";

         new GuiTextEditCtrl() {
            canSaveDynamicFields = "0";
            internalName = "softSnapSizeTextEdit";
            isContainer = "0";
            HorizSizing = "right";
            profile="ToolsGuiNumericDropSliderTextProfile";
            VertSizing = "bottom";
            position = "0 2";
            Extent = "42 16";
            MinExtent = "8 16";
            canSave = "1";
            Visible = "1";
            AltCommand = "EWorldEditor.setSoftSnapSize( $ThisControl.getText() ); EWorldEditor.syncGui();";
            tooltip = "Object Snapping Distance";
            hovertime = "1000";
            text = "9";
            maxLength = "6";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "34 2";
            Extent = "18 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "Canvas.pushDialog(softSnapSizeSliderCtrlContainer);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Changes size of the soft snap region";
            hovertime = "750";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:dropslider_n_image";
         };
      };
      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         position = "269 3";
         Extent = "2 26";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };
         
      new GuiBitmapButtonCtrl() {
         canSaveDynamicFields = "0";
         internalName = "boundingBoxColBtn";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "274 3";
         Extent = "29 27";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Variable = "EWorldEditor.boundingBoxCollision";
         Command = "EWorldEditor.boundingBoxCollision = $ThisControl.getValue();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Object bounds selection toggle (V)";
         hovertime = "1000";
         bitmapAsset = "ToolsModule:select_bounds_n_image";
         groupNum = "-1";
         buttonType = "ToggleButton";
         useMouseEvents = "0";
      };
         
      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         position = "307 3";
         Extent = "2 26";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };
      
      new GuiControl(ToggleButtonBar){
         isContainer = "1";
         profile = "ToolsMenubarProfile";
         Position = "313 3";
         Extent = "65 27";
         
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "centerObject";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "0 0";
            Extent = "29 27";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "objectCenterDropdown.toggle();";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Toggles object center (O) and bounds center (P)";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:object_center_n_image";
            text = "Button";
            groupNum = "-1";
            buttonType = "ToggleButton";
            useMouseEvents = "0";
            
            new GuiBitmapCtrl(){
               HorizSizing = "left";
               VertSizing = "top";
               Position = "23 21";
               Extent = "4 4";
               MinExtent = "4 4";
               bitmapAsset = "ToolsModule:dropdown_button_arrow_image";
            };
         };

         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "objectTransform";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "31 0";
            Extent = "29 27";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "objectTransformDropdown.toggle();";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Toggles object transform (K) and world transform (L)";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:world_transform_n_image";
            groupNum = "-1";
            buttonType = "ToggleButton";
            text = "";  
            
            new GuiBitmapCtrl(){
               HorizSizing = "left";
               VertSizing = "top";
               Position = "23 21";
               Extent = "4 4";
               MinExtent = "4 4";
               bitmapAsset = "ToolsModule:dropdown_button_arrow_image";
            };  
         };
      };
      
      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         position = "379 3";
         Extent = "2 26";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };
            
      new GuiControl(ToggleNodeBar){
         isContainer = "1";
         profile = "ToolsMenubarProfile";
         Position = "386 3";
         Extent = "63 27";
         
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "renderHandleBtn";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "0 0";
            Extent = "29 27";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Variable = "EWorldEditor.renderObjHandle";
            Command = "EWorldEditor.renderObjHandle = $ThisControl.getValue();";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Enables Render of Object Node Icons (N)";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:object_node_icon_n_image";
            groupNum = "-1";
            buttonType = "ToggleButton";
            useMouseEvents = "0";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "renderTextBtn";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "33 0";
            Extent = "29 27";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Variable = "EWorldEditor.renderObjText";
            Command = "EWorldEditor.renderObjText = $ThisControl.getValue();";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Enables Render of Object Node Lables (SHIFT N)";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:object_node_lable_n_image";
            text = "";
            groupNum = "-1";
            buttonType = "ToggleButton";
            useMouseEvents = "0";
         };
      };
      
      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         position = "379 3";
         Extent = "2 26";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };
            
      new GuiControl(PrefabBar){
         isContainer = "1";
         profile = "ToolsMenubarProfile";
         Position = "386 3";
         Extent = "63 27";
         visible = true;
         
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "makePrefabBtn";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "0 0";
            Extent = "29 27";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Variable = "";
            Command = "EditorMakePrefab();";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Make the Selection a Prefab.";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:selection_to_prefab_n_image";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "explodePrefabBtn";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "33 0";
            Extent = "29 27";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Variable = "";
            Command = "EditorExplodePrefab();";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Explode the Selected Prefab.";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:explode_prefab_n_image";
            text = "";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
         };
      };
      
      new GuiBitmapButtonCtrl() {
         canSaveDynamicFields = "0";
         internalName = "bakeProbesBtn";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "452 3";
         Extent = "29 27";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Variable = "";
         Command = "updateReflectionProbes();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Update Reflection Probes";
         hovertime = "1000";
         bitmapAsset = "ToolsModule:probe_n_image";
         text = "";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      
      new GuiContainer(objectCenterDropdown){
         Profile = "IconDropdownProfile";
         Position = getWord(EWorldEditorToolbar.position, 0)+getWord(ToggleButtonBar.Position, 0)+getWord(EWorldEditorToolbar-->centerObject.position, 0)-5 SPC getWord(EditorGuiToolbar.extent, 1)-1;
         Extent = "132 62";
         isContainer = "1";
         visible = "0"; 
         
         new GuiIconButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "objectBoxBtn";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiIconButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 5";
            Extent = "122 25";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "EWorldEditor.objectsUseBoxCenter = 0; EWorldEditor.syncGui(); objectCenterDropdown.toggle(); ";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Use object defined center (O)";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:object_center_n_image";
            text = "Object Center";
            buttonMargin = "0 4";
            textMargin = "38";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
         };
         new GuiIconButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "objectBoundsBtn";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiIconButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 33 ";
            Extent = "122 25";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "EWorldEditor.objectsUseBoxCenter = 1; EWorldEditor.syncGui(); objectCenterDropdown.toggle(); ";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Use bounding box center (P)";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:bounds_center_n_image";
            text = "Bounds Center";
            buttonMargin = "0 4";
            textMargin = "38";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
         };
         
         new GuiDecoyCtrl(objectCenterDropdownDecoy) {
            profile = "ToolsGuiDefaultProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "0 0";
            extent = "132 62";
            minExtent = "8 8";
            visible = "1";
            helpTag = "0";
            useMouseEvents = "1";
            isDecoy = "1";
         };
      };

      new GuiContainer(objectTransformDropdown){
         Profile = "IconDropdownProfile";
         Position = getWord(EWorldEditorToolbar.position, 0)+getWord(ToggleButtonBar.position, 0)+getWord(EWorldEditorToolbar-->objectTransform.position, 0)-5 SPC getWord(EditorGuiToolbar.extent, 1)-1;
         Extent = "147 62";
         isContainer = "1";
         visible ="0";
         
         new GuiIconButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "worldTransformBtn";
            Enabled = "1";
            isContainer = "0";
            Profile = "GuiIconProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 5";
            Extent = "137 25";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "GlobalGizmoProfile.setFieldValue(alignment, World);  EWorldEditor.syncGui(); objectTransformDropdown.toggle(); ";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Use world normal for transformations (L)";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:world_transform_n_image";
            text = "World Transform";
            buttonMargin = "0 4";
            textMargin = "38";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
         };
         
         new GuiIconButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "objectTransformBtn";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiIconButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 33";
            Extent = "137 25";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "GlobalGizmoProfile.setFieldValue(alignment, Object); EWorldEditor.syncGui(); objectTransformDropdown.toggle(); ";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Use object normal for transformations (K)";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:object_transform_n_image";
            text = "Object Transform";
            buttonMargin = "0 4";
            textMargin = "38";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
         };
         
         new GuiDecoyCtrl(objectTransformDropdownDecoy) {
            profile = "ToolsGuiDefaultProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "0 0";
            extent = "147 62";
            minExtent = "8 8";
            visible = "1";
            helpTag = "0";
            useMouseEvents = "1";
            isDecoy = "1";
         };
      };
   };
};

new GuiMouseEventCtrl(softSnapSizeSliderCtrlContainer, EditorGuiGroup) {
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 8";
   visible = "1";
   helpTag = "0";
   class = "EditorDropdownSliderContainer";
   
   new GuiSliderCtrl() {
      canSaveDynamicFields = "0";
      internalName = "slider";
      isContainer = "0";
      Profile = "ToolsGuiSliderBoxProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = EWorldEditorToolbar.position.x + EWorldEditorToolbar-->softSnapSizeTextEditContainer.position.x + 50 SPC 
         EWorldEditorToolbar-->softSnapSizeTextEdit.position.y + 50;
      Extent = "112 20";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      AltCommand = "softSnapSizeSliderCtrlContainer.onSliderChanged();";
      range = "0.01 10";
      ticks = "0";
      value = "0";
   };
};
