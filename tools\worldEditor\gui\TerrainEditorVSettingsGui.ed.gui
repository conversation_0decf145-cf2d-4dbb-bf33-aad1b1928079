//--- OBJECT WRITE BEGIN ---
new GuiControl(TerrainEditorValuesSettingsGui, EditorGuiGroup) {
   profile = "ToolsGuiOverlayProfile";
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "640 480";
   minExtent = "8 8";
   visible = "1";
   helpTag = "0";

   new GuiWindowCtrl() {
      profile = "ToolsGuiWindowProfile";
      horizSizing = "center";
      vertSizing = "center";
      position = "117 113";
      extent = "408 247";
      minExtent = "8 8";
      visible = "1";
      helpTag = "0";
      text = "Terrain Action Values";
      maxLength = "255";
      resizeWidth = "0";
      resizeHeight = "0";
      canMove = "1";
      canClose = "0";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "50 50";
      closeCommand = "Canvas.popDIalog(TerrainEditorValuesSettingsGui);";

      new GuiControl() {
         profile = "ToolsGuiWindowProfile";
         horizSizing = "right";
         vertSizing = "bottom";
         position = "198 27";
         extent = "203 115";
         minExtent = "8 8";
         visible = "1";
         helpTag = "0";

         new GuiTextEditCtrl() {
            profile = "ToolsGuiTextEditProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "86 12";
            extent = "107 18";
            minExtent = "8 8";
            visible = "1";
            variable = "ETerrainEditor.adjustHeightVal";
            command = "ETerrainEditor.adjustHeightVal = $ThisControl.getValue();";
            helpTag = "0";
            maxLength = "255";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Adjust height increment.";
         };
         new GuiTextEditCtrl() {
            profile = "ToolsGuiTextEditProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "86 62";
            extent = "107 18";
            minExtent = "8 8";
            visible = "1";
            variable = "ETerrainEditor.scaleVal";
            command = "ETerrainEditor.scaleVal = $ThisControl.getValue();";
            helpTag = "0";
            maxLength = "255";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Scale height increment.";
         };
         new GuiTextEditCtrl() {
            profile = "ToolsGuiTextEditProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "86 87";
            extent = "107 18";
            minExtent = "8 8";
            visible = "1";
            variable = "ETerrainEditor.smoothFactor";
            command = "ETerrainEditor.smoothFactor = $ThisControl.getValue();";
            helpTag = "0";
            maxLength = "255";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Smoothing factor -- lower values are less agressive.";
         };
         new GuiTextCtrl() {
            profile = "ToolsGuiTextProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "11 12";
            extent = "64 18";
            minExtent = "8 8";
            visible = "1";
            helpTag = "0";
            text = "Adjust Height";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Adjust height increment.";
            maxLength = "255";
         };
         new GuiTextCtrl() {
            profile = "ToolsGuiTextProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "11 37";
            extent = "49 18";
            minExtent = "8 8";
            visible = "1";
            helpTag = "0";
            text = "Set Height";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Elevation for set height operation.";
            maxLength = "255";
         };
         new GuiTextCtrl() {
            profile = "ToolsGuiTextProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "11 62";
            extent = "60 18";
            minExtent = "8 8";
            visible = "1";
            helpTag = "0";
            text = "Scale Height";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Scale height increment.";
            maxLength = "255";
         };
         new GuiTextCtrl() {
            profile = "ToolsGuiTextProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "10 87";
            extent = "70 18";
            minExtent = "8 8";
            visible = "1";
            helpTag = "0";
            text = "Smooth Factor";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Smoothing factor -- lower values are less agressive.";
            maxLength = "255";
         };
      };
      new GuiButtonCtrl() {
         profile = "ToolsGuiButtonProfile";
         horizSizing = "right";
         vertSizing = "bottom";
         position = "218 205";
         extent = "80 20";
         minExtent = "8 8";
         visible = "1";
         command = "Canvas.popDIalog(TerrainEditorValuesSettingsGui);";
         helpTag = "0";
         text = "OK";
         groupNum = "-1";
         buttonType = "PushButton";
      };
      new GuiControl() {
         profile = "ToolsGuiWindowProfile";
         horizSizing = "right";
         vertSizing = "bottom";
         position = "7 27";
         extent = "188 212";
         minExtent = "8 8";
         visible = "1";
         helpTag = "0";

         new GuiFilterCtrl(TESoftSelectFilter) {
            profile = "ToolsGuiDefaultProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "20 22";
            extent = "155 162";
            minExtent = "8 8";
            visible = "1";
            helpTag = "0";
            controlPoints = "7";
            filter = "1.000000 0.833333 0.666667 0.500000 0.333333 0.166667 0.000000";
         };
         new GuiTextCtrl() {
            profile = "ToolsGuiTextProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "6 4";
            extent = "67 18";
            minExtent = "8 8";
            visible = "1";
            helpTag = "0";
            text = "Soft Selection";   
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "This spline scale modifies the hardness of the brush.  Left is center, right is outer edge.";
            maxLength = "255";
         };
         new GuiTextCtrl() {
            profile = "ToolsGuiTextProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "12 189";
            extent = "8 18";
            minExtent = "8 8";
            visible = "1";
            helpTag = "0";
            text = "0";
            maxLength = "255";
         };
         new GuiTextCtrl() {
            profile = "ToolsGuiTextProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "12 26";
            extent = "8 18";
            minExtent = "8 8";
            visible = "1";
            helpTag = "0";
            text = "1";
            maxLength = "255";
         };
         new GuiTextCtrl() {
            profile = "ToolsGuiTextProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "60 190";
            extent = "45 18";
            minExtent = "8 8";
            visible = "1";
            helpTag = "0";
            text = "<Radius>";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Brush radius for Selection Mode.";
            maxLength = "255";
         };
         new GuiTextEditCtrl() {
            profile = "ToolsGuiTextEditProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "125 187";
            extent = "50 18";
            minExtent = "8 8";
            visible = "1";
            variable = "ETerrainEditor.softSelectRadius";
            command = "ETerrainEditor.softSelectRadius = $ThisControl.getValue();";
            helpTag = "0";
            maxLength = "255";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Brush radius for Selection Mode.";
         };
      };
      new GuiButtonCtrl(TESettingsApplyButton) {
         profile = "ToolsGuiButtonProfile";
         horizSizing = "right";
         vertSizing = "bottom";
         position = "307 205";
         extent = "80 20";
         minExtent = "8 8";
         visible = "1";
         helpTag = "0";
         text = "Apply";
         groupNum = "-1";
         buttonType = "PushButton";
      };
   };
};
//--- OBJECT WRITE END ---

