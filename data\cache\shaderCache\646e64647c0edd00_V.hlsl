//*****************************************************************************
// Torque -- HLSL procedural shader
//*****************************************************************************

// Dependencies:
#include "core/rendering/shaders/torque.hlsl"

// Features:
// Vert Position
// Background Object
// Base Texture
// Diffuse Vertex Color
// Deferred Shading: PBR Config Explicit Numbers
// Deferred Shading: Mat Info Flags
// Fog
// Debug Viz
// HDR Output
// Translucent
// Forward Shaded Material

struct VertData
{
   float3 position        : POSITION;
   float4 diffuse         : COLOR;
   float2 texCoord        : TEXCOORD0;
};


struct ConnectData
{
   float4 hpos            : SV_Position;
   float4 vertColor       : COLOR;
   float2 out_texCoord    : TEXCOORD0;
   float3 outWsPosition   : TEXCOORD1;
};


//-----------------------------------------------------------------------------
// Main
//-----------------------------------------------------------------------------
ConnectData main( VertData IN,
                  uniform float4x4 modelview       : register(C0),
                  uniform float4x4 objTrans        : register(C4)
)
{
   ConnectData OUT;

   // Vert Position
   OUT.hpos = mul(modelview, float4(IN.position.xyz,1));
   OUT.hpos = OUT.hpos.xyww;
   
   // Background Object
   
   // Base Texture
   OUT.out_texCoord = (float2)IN.texCoord;
   
   // Diffuse Vertex Color
   OUT.vertColor = IN.diffuse;
   
   // Deferred Shading: PBR Config Explicit Numbers
   
   // Deferred Shading: Mat Info Flags
   
   // Fog
   OUT.outWsPosition = mul( objTrans, float4( IN.position.xyz, 1 ) ).xyz;
   
   // Debug Viz
   
   // HDR Output
   
   // Translucent
   
   // Forward Shaded Material
   
   return OUT;
}
