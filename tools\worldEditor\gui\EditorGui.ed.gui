//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiContainer(<PERSON><PERSON><PERSON>,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "800 600";
   MinExtent = "8 8";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   Margin = "0 0 0 0";
   Padding = "0 0 0 0";
   AnchorTop = "1";
   AnchorBottom = "0";
   AnchorLeft = "1";
   AnchorRight = "0";
   
   new GuiContainer(EditorGuiToolbar) {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsMenubarProfile";
      HorizSizing = "width";
      VertSizing = "bottom";
      Position = "0 10";
      Extent = "800 32";
      MinExtent = "8 8";
      canSave = "1";
      Visible = "1";
      hovertime = "1000";
      
      new GuiStackControl(EditorGuiToolbarStack) {
         stackingType = "Horizontal";
         horizStacking = "Left to Right";
         vertStacking = "Top to Bottom";
         padding = "0";
         dynamicSize = "1";
         dynamicNonStackExtent = "0";
         dynamicPos = "0";
         changeChildSizeToFit = "0";
         changeChildPosition = "1";
         position = "0 0";
         extent = "885 32";
         minExtent = "16 16";
         horizSizing = "right";
         vertSizing = "bottom";
         profile = "GuiDefaultProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "GuiToolTipProfile";
         hovertime = "1000";
         isContainer = "1";
         canSave = "1";
         canSaveDynamicFields = "0";
        
      new GuiBitmapButtonCtrl(EHWorldEditor) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "4 3";
         Extent = "29 27";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Command = "";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Open the WorldEditor";
         hovertime = "1000";
         bitmapAsset = "ToolsModule:world_image";
         groupNum = "0";
         buttonType = "RadioButton";
         useMouseEvents = "0";
      };
      new GuiBitmapButtonCtrl(EHGuiEditor) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "34 3";
         Extent = "29 27";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Command = "toggleGuiEditor(true); $GuiEditorBtnPressed = true;";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Open the GuiEditor";
         hovertime = "1000";
         bitmapAsset = "ToolsModule:gui_image";
         groupNum = "0";
         buttonType = "RadioButton";
         useMouseEvents = "0";
      };
      new GuiBitmapButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "64 3";
         Extent = "29 27";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Command = "Editor.close(ProjectSettings.value(\"UI/playGUIName\"));";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Play Game";
         hovertime = "1000";
         bitmapAsset = "ToolsModule:playbutton_n_image";
         groupNum = "0";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
     
      new GuiBitmapCtrl() {
      Enabled = "1";
      Profile = "ToolsGuiDefaultProfile";
      position = "98 3";
      Extent = "2 26";
      MinExtent = "1 1";
      bitmapAsset = "ToolsModule:separator_h_image";
      };
      
         new GuiBitmapButtonCtrl() {
            BitmapAsset = "ToolsModule:settings_n_image";
            bitmapMode = "Stretched";
            autoFitExtents = "0";
            useModifiers = "0";
            useStates = "1";
            masked = "0";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            position = "572 0";
            extent = "29 27";
            minExtent = "8 8";
            horizSizing = "right";
            vertSizing = "bottom";
            profile = "ToolsGuiButtonProfile";
            visible = "1";
            active = "1";
            command = "ESettingsWindow.toggleEditorSettings();";
            tooltipProfile = "ToolsGuiToolTipProfile";
            tooltip = "Open Editor Settings";
            hovertime = "1000";
            isContainer = "0";
            internalName = "editorSettingsBtn";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
         
         new GuiBitmapCtrl() {
            Enabled = "1";
            Profile = "ToolsGuiDefaultProfile";
            position = "98 3";
            Extent = "2 26";
            MinExtent = "1 1";
            bitmapAsset = "ToolsModule:separator_h_image";
         };
         
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = AssetBrowserBtn;
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "180 0";
            Extent = "29 27";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "AssetBrowser.toggleDialog();";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Asset Browser";
            hovertime = "750";
            bitmapAsset = "ToolsModule:menuGrid_n_image";
            bitmapMode = "Stretched";
            buttonType = "PushButton";
            groupNum = "0";
            useMouseEvents = "0";
         };
         
         new GuiBitmapCtrl() {
            Enabled = "1";
            Profile = "ToolsGuiDefaultProfile";
            position = "98 3";
            Extent = "2 26";
            MinExtent = "1 1";
            bitmapAsset = "ToolsModule:separator_h_image";
         };
         
      new GuiBitmapButtonCtrl(EWorldEditorToggleCamera) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "102 3";
         Extent = "29 27";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Command = "CameraTypesDropdownToggle();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Toggle Camera Modes";
         hovertime = "1000";
         bitmapAsset = "ToolsModule:player_n_image";
         groupNum = "-1";
         buttonType = "ToggleButton";
         useMouseEvents = "0";
         
         new GuiBitmapCtrl(){
            HorizSizing = "left";
            VertSizing = "top";
            Position = getWord(EWorldEditorToggleCamera.extent, 0)-6 SPC getWord(EWorldEditorToggleCamera.extent, 1)-6;
            Extent = "4 4";
            MinExtent = "4 4";
            bitmapAsset = "ToolsModule:dropdown_button_arrow_image";
         };
      };
      new GuiControl(CameraSpeedDropdownContainer, EditorGuiGroup) {
         canSaveDynamicFields = "0";
         isContainer = "1";
         Profile = "ToolsGuiTransparentProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "136 5";
         Extent = "136 27";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";

         new GuiTextCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "0 6";
            Extent = "78 10";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "Camera Speed";
            maxLength = "1024";
         };
         new GuiTextEditCtrl(EWorldEditorCameraSpeed) {
            canSaveDynamicFields = "0";
            internalName = "textEdit";
            isContainer = "0";
            profile="ToolsGuiNumericDropSliderTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "78 2";
            Extent = "42 16";
            MinExtent = "8 16";
            canSave = "1";
            Visible = "1";
            Command = "EWorldEditorCameraSpeed.updateMenuBar( $ThisControl );";
            hovertime = "1000";
            text = "100";
            maxLength = "4";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "112 2";
            Extent = "18 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "Canvas.pushDialog(CameraSpeedDropdownCtrlContainer);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Changes the Camera Speed";
            hovertime = "750";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:dropslider_n_image";
         };
      };
      /*new GuiPopUpMenuCtrl(EWorldEditorCameraSpeed) {
         canSaveDynamicFields = "0";
         internalName = "CameraSpeedDropdown";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiPopUpMenuProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "136 7";
         Extent = "130 18";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         maxLength = "1024";
         maxPopupHeight = "200";
         sbUsesNAColor = "0";
         reverseTextList = "0";
         bitmapBounds = "16 16";
      };*/
      new GuiBitmapButtonCtrl(visibilityToggleBtn) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "270 3";
         Extent = "29 27";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Command = "VisibilityDropdownToggle();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Toggle Visibility Modes (ALT V)";
         hovertime = "1000";
         bitmapAsset = "ToolsModule:visibility_toggle_n_image";
         groupNum = "-1";
         buttonType = "ToggleButton";
         useMouseEvents = "0";
         
         new GuiBitmapCtrl(){
            HorizSizing = "left";
            VertSizing = "top";
            Position = getWord(visibilityToggleBtn.extent, 0)-6 SPC getWord(visibilityToggleBtn.extent, 1)-6;
            Extent = "4 4";
            MinExtent = "4 4";
            bitmapAsset = "ToolsModule:dropdown_button_arrow_image";
         };
      };
      
      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         position = "303 3";
         Extent = "2 26";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };
            
      new GuiPopUpMenuCtrl(EWorldEditorAlignPopup) {
         canSaveDynamicFields = "0";
         internalName = "";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiPopUpMenuProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "439 2";
         Extent = "70 18";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "0";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         maxLength = "1024";
         maxPopupHeight = "200";
         sbUsesNAColor = "0";
         reverseTextList = "0";
         bitmapBounds = "16 16";
         };
      };
   };
   
   new GuiContainer(EditorGuiStatusBar) {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsMenubarProfile";
      HorizSizing = "width";
      VertSizing = "top";
      Position = "0 578";
      Extent = "800 35";
      MinExtent = "8 8";
      canSave = "1";
      Visible = "1";
      hovertime = "1000";
      Docking = "Bottom";
      
      new GuiTextCtrl(EWorldEditorStatusBarInfo) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "width";
         VertSizing = "bottom";
         Position = "10 8";
         Extent = "450 18";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "Current Tool";
         maxLength = "255";
      };
      
      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         position = "465 8";
         Extent = "2 18";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };
      
      new GuiTextCtrl(EWorldEditorStatusBarSelection) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         Position = "485 8";
         Extent = "180 18";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "Ready.";
         maxLength = "255";
      };
      
      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         position = "645 8";
         Extent = "2 18";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };
      
      new GuiPopUpMenuCtrl(EWorldEditorStatusBarCamera) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiPopUpMenuProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         Position = "665 8";
         Extent = "120 18";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
      };
      
      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         position = "800 2";
         Extent = "2 18";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };
   };
   
   new WorldEditor(EWorldEditor) {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "0";
      Profile = "WorldEditorProfile";
      HorizSizing = "width";
      VertSizing = "height";
      Position = "0 0";
      Extent = "800 600";
      MinExtent = "8 8";
      canSave = "1";
      Visible = "1";
      hovertime = "1000";
      Docking = "None";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "0";
      AnchorBottom = "0";
      AnchorLeft = "0";
      AnchorRight = "0";
      cameraZRot = "0";
      forceFOV = "0";
      renderMissionArea = "0";
      missionAreaFillColor = "255 0 0 20";
      missionAreaFrameColor = "255 0 0 128";
      allowBorderMove = "0";
      borderMovePixelSize = "20";
      borderMoveSpeed = "0.1";
      consoleFrameColor = "255 0 0 255";
      consoleFillColor = "0 0 0 0";
      consoleSphereLevel = "1";
      consoleCircleSegments = "32";
      consoleLineWidth = "1";
      GizmoProfile = "GlobalGizmoProfile";
      isDirty = "0";
      stickToGround = "0";
      dropAtBounds = "1";
      dropBelowCameraOffset = "15";
      dropType = "screenCenter";
      boundingBoxCollision = "1";
      renderPopupBackground = "1";
      popupBackgroundColor = "100 100 100 255";
      popupTextColor = "255 255 0 255";
      objectTextColor = "255 255 255 255";
      objectsUseBoxCenter = "1";
      objSelectColor = "255 0 0 255";
      objMouseOverSelectColor = "0 0 255 255";
      objMouseOverColor = "0 255 0 255";
      showMousePopupInfo = "1";
      dragRectColor = "255 255 0 255";
      renderObjText = "1";
      renderObjHandle = "1";
      objTextFormat = "$name|class$";
      faceSelectColor = "0 0 100 100";
      renderSelectionBox = "1";
      selectionBoxColor = "255 255 0 255";
      selectionLocked = "0";
      toggleIgnoreList = "0";
      selectHandle = "ToolsModule:SelectHandle_image";
      defaultHandle = "ToolsModule:DefaultHandle_image";
      lockedHandleAsset = "ToolsModule:LockedHandle_image";
   };
   new TerrainEditor(ETerrainEditor) {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "0";
      Profile = "WorldEditorProfile";
      HorizSizing = "width";
      VertSizing = "height";
      Position = "0 0";
      Extent = "800 600";
      MinExtent = "8 8";
      canSave = "1";
      Visible = "0";
      hovertime = "1000";
      Docking = "None";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "0";
      AnchorBottom = "0";
      AnchorLeft = "0";
      AnchorRight = "0";
      cameraZRot = "0";
      forceFOV = "0";
      renderMissionArea = "0";
      missionAreaFillColor = "0 0 0 20";//"255 0 0 20";
      missionAreaFrameColor = "0 0 0 128";//"255 0 0 128";
      allowBorderMove = "0";
      borderMovePixelSize = "20";
      borderMoveSpeed = "0.1";
      consoleFrameColor = "0 0 0 255";
      consoleFillColor = "0 0 0 0";
      consoleSphereLevel = "1";
      consoleCircleSegments = "32";
      consoleLineWidth = "1";
      GizmoProfile = "GlobalGizmoProfile";
      isDirty = "0";
      isMissionDirty = "0";
      renderBorder = "1";
      borderHeight = "10";
      borderFillColor = "0 255 0 20";
      borderFrameColor = "0 255 0 128";
      borderLineMode = "0";
      selectionHidden = "1";
      renderVertexSelection = "1";
      processUsesBrush = "0";
      maxBrushSize = "256 256";
      adjustHeightVal = "10";
      setHeightVal = "100";
      scaleVal = "1";
      smoothFactor = "0.1";
      materialGroup = "0";
      softSelectRadius = "50";
      softSelectFilter = "1.000000 0.833333 0.666667 0.500000 0.333333 0.166667 0.000000";
      softSelectDefaultFilter = "1.000000 0.833333 0.666667 0.500000 0.333333 0.166667 0.000000";
      adjustHeightMouseScale = "0.1";
      paintIndex = "-1";

      new GuiTextCtrl(TESelectionInfo) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "EditorTextProfile";
         HorizSizing = "right";
         VertSizing = "top";
         Position = "288 549";
         Extent = "120 18";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = " (Selection) #: 0  avg: 0";
         maxLength = "255";
      };
      new GuiTextCtrl(TEMouseBrushInfo) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "EditorTextProfile";
         HorizSizing = "right";
         VertSizing = "top";
         Position = "40 549";
         Extent = "107 18";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = " (Mouse) #: 0  avg: 0";
         maxLength = "255";
      };
      new GuiTextCtrl(TESelectionInfo1) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "EditorTextProfileWhite";
         HorizSizing = "right";
         VertSizing = "top";
         Position = "289 550";
         Extent = "120 18";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = " (Selection) #: 0  avg: 0";
         maxLength = "255";
      };
      new GuiTextCtrl(TEMouseBrushInfo1) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "EditorTextProfileWhite";
         HorizSizing = "right";
         VertSizing = "top";
         Position = "41 550";
         Extent = "107 18";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = " (Mouse) #: 0  avg: 0";
         maxLength = "255";
      };
   };
    
   new GuiControl(RelightStatus) {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiSolidDefaultProfile";
      HorizSizing = "center";
      VertSizing = "center";
      Position = "223 277";
      Extent = "353 45";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "0";
      hovertime = "1000";
       

      new GuiProgressBitmapCtrl(RelightProgress) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "ToolsGuiRLProgressBitmapProfile";
         HorizSizing = "center";
         VertSizing = "center";
         position = "5 0";
         Extent = "440 24";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         maxLength = "1024";

      };
      new GuiTextCtrl(RelightProgressTxt) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiProgressTextProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "5 20";
         Extent = "440 20";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "Loading Mission";
         maxLength = "255";
      };
   };
   new GuiControl(RelightMessage) {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiSolidDefaultProfile";
      HorizSizing = "width";
      VertSizing = "top";
      Position = "19 570";
      Extent = "583 23";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "0";
      hovertime = "1000";

      new GuiTextCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "width";
         VertSizing = "bottom";
         Position = "5 1";
         Extent = "449 18";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "A lightmapped object has been altered; relight the scene!";
         maxLength = "255";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         Position = "468 2";
         Extent = "75 18";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "Editor.lightScene(\"\", forceAlways); RelightMessage.visible = false;";
         hovertime = "1000";
         text = "Relight Scene";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         Position = "548 2";
         Extent = "32 18";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "RelightMessage.visible = false;";
         hovertime = "1000";
         text = "Hide";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
   };
   new GuiControl(PhysicsEditMessage) {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiSolidDefaultProfile";
      HorizSizing = "center";
      VertSizing = "top";
      Position = "180 560";
      Extent = "440 32";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "0";
      hovertime = "1000";

      new GuiTextCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "EditorTextProfile";
         HorizSizing = "width";
         VertSizing = "center";
         Position = "5 0";
         Extent = "238 18";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "PHYSICS SIMULATION PAUSED FOR EDITING...";
         maxLength = "255";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         Position = "337 3";
         Extent = "43 26";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "physicsStart(); PhysicsEditMessage.visible = false;";
         hovertime = "1000";
         text = "Start";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         Position = "392 3";
         Extent = "43 26";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "PhysicsEditMessage.visible = false;";
         hovertime = "1000";
         text = "Hide";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
   };
   new GuiContainer(CameraTypesDropdown){
      Profile = "IconDropdownProfile";
      Position = getWord(EWorldEditorToggleCamera.position, 0)-5 SPC getWord(EditorGuiToolbar.extent, 1)-1;
      Extent = "137" SPC ((6*28)+6);//97";
      isContainer = "1";
      visible = "0"; 
         
      new GuiDynamicCtrlArrayControl(cameraDropdownArray) {
        canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "ToolsGuiSolidDefaultProfile";
         HorizSizing = "width";
         VertSizing = "height";
         Position = "5 5";
         Extent = "132" SPC getWord(CameraTypesDropdown.extent, 1)-5;
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         colCount = "1";
         colSize = "127";
         rowCount = "0";
         RowSize = "64";
         rowSpacing = "3";
         colSpacing = "3";
         autoCellSize = "1";
         fillRowFirst = "0";
         
         new GuiIconButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "StandardCamera";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiIconButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 5";
            Extent = "127 25";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "CameraTypesDropdownToggle(); EditorGuiStatusBar.setCamera(\"Standard Camera\");";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Free Camera";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:camera_n_image";
            groupNum = "0";
            text="Free Camera";
            buttonMargin = "0 4";
            textMargin = "38";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
         };
         new GuiIconButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "OrbitCamera";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiIconButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 32";
            Extent = "127 25";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "CameraTypesDropdownToggle(); EditorGuiStatusBar.setCamera(\"Orbit Camera\");";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Toggle Orbit Cam";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:orbit_cam_n_image";
            groupNum = "0";
            text="Orbit Camera";
            buttonMargin = "0 4";
            textMargin = "38";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
         };
         
         new GuiIconButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "PlayerCamera";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiIconButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 5";
            Extent = "127 25";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "CameraTypesDropdownToggle(); EditorGuiStatusBar.setCamera(\"1st Person Camera\");";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Player Camera";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:player_n_image";
            groupNum = "0";
            text="Player Camera";
            buttonMargin = "0 4";
            textMargin = "38";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
         };
         new GuiIconButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "trdPersonCamera";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiIconButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 5";
            Extent = "127 25";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "CameraTypesDropdownToggle(); EditorGuiStatusBar.setCamera(\"3rd Person Camera\");";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "3rd Person Camera";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:3rd_person_camera_n_image";
            groupNum = "0";
            text="3rd Person Cam";
            buttonMargin = "0 4";
            textMargin = "38";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
         };
         new GuiIconButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "NewtonianCamera";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiIconButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 64";
            Extent = "127 25";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "CameraTypesDropdownToggle(); EditorGuiStatusBar.setCamera(\"Smooth Camera\");";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Toggle Newtonian Cam";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:smooth_cam_n_image";
            groupNum = "0";
            text="Smooth Camera";
            buttonMargin = "0 4";
            textMargin = "38";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
         };
         new GuiIconButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "NewtonianRotationCamera";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiIconButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 64";
            Extent = "127 25";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "CameraTypesDropdownToggle(); EditorGuiStatusBar.setCamera(\"Smooth Rot Camera\");";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Toggle Smooth Camera with Smooth Rotation";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:smooth_cam_rot_n_image";
            groupNum = "0";
            text="Smooth Rotate";
            buttonMargin = "0 4";
            textMargin = "38";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
         };
      };
      
      new GuiDecoyCtrl(CameraTypesDropdownDecoy) {
         profile = "ToolsGuiDefaultProfile";
         horizSizing = "right";
         vertSizing = "bottom";
         position = "0 0";
         extent = getWord(CameraTypesDropdown.extent, 0) SPC getWord(CameraTypesDropdown.extent, 1);
         minExtent = "8 8";
         visible = "1";
         helpTag = "0";
         useMouseEvents = "1";
         isDecoy = "1";
      };
      
   };
   new GuiContainer(VisibilityDropdown){
      Profile = "IconDropdownProfile";
      Position = getWord(visibilityToggleBtn.position, 0)-5 SPC getWord(EditorGuiToolbar.extent, 1)-1;
      Extent = "159 261"; //SPC ((6*28)+6);//97";
      isContainer = "1";
      visible = "0"; 
      
      new GuiTabBookCtrl() {
         canSaveDynamicFields = "0";
         Profile = "ToolsGuiTabBookProfile";
         HorizSizing = "width";
         VertSizing = "height";
         Docking = "Client";
         Margin = "3 3 3 3";
         Position = "5 24";
         Extent = "170 226";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         TabPosition = "Top";
         TabHeight = "22";
         TabMargin = "7";
         MinTabWidth = "64";

         new GuiTabPageCtrl() {
            canSaveDynamicFields = "0";
            Profile = "ToolsGuiTabPageProfile";
            HorizSizing = "width";
            VertSizing = "height";
            Docking = "Client";
            Margin = "-1 0 0 0";
            Position = "0 14";
            Extent = "164 220";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "0";
            hovertime = "1000";
            text = "Viz Toggles";
            maxLength = "255";
            
            new GuiScrollCtrl() {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "1";
               Profile = "ToolsGuiScrollProfile";
               HorizSizing = "width";
               VertSizing = "height";
               Docking = "Client";
               Position = "4 12";
               Extent = "156 190";
               MinExtent = "8 2";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               Margin = "0 0 0 0";
               Padding = "0 0 0 0";
               AnchorTop = "1";
               AnchorBottom = "0";
               AnchorLeft = "1";
               AnchorRight = "0";
               willFirstRespond = "1";
               hScrollBar = "alwaysOff";
               vScrollBar = "dynamic";
               lockHorizScroll = "true";
               lockVertScroll = "false";
               constantThumbHeight = "0";
               childMargin = "2 0";

               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  Padding = "-2";
                  canSaveDynamicFields = "0";
                  internalName = "theVisOptionsList";
                  Enabled = "1";
                  isContainer = "1";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  Position = "1 0";
                  Extent = "156 16";
                  MinExtent = "16 16";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
               };
            };
         };
         new GuiTabPageCtrl() {
            canSaveDynamicFields = "0";
            Profile = "ToolsGuiTabPageProfile";
            HorizSizing = "width";
            VertSizing = "height";
            Docking = "Client";
            Margin = "-1 0 0 0";
            Position = "0 14";
            Extent = "164 220";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "0";
            hovertime = "1000";
            text = "Class Toggles";
            maxLength = "255";
            
            new GuiScrollCtrl() {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "1";
               Profile = "ToolsGuiScrollProfile";
               HorizSizing = "width";
               VertSizing = "height";
               Docking = "Client";
               Position = "4 12";
               Extent = "156 190";
               MinExtent = "8 2";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               Margin = "0 0 0 0";
               Padding = "0 0 0 0";
               AnchorTop = "1";
               AnchorBottom = "0";
               AnchorLeft = "1";
               AnchorRight = "0";
               willFirstRespond = "1";
               hScrollBar = "alwaysOff";
               vScrollBar = "dynamic";
               lockHorizScroll = "true";
               lockVertScroll = "false";
               constantThumbHeight = "0";
               childMargin = "2 0";

               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  Padding = "-2";
                  canSaveDynamicFields = "0";
                  internalName = "theClassVisList";
                  Enabled = "1";
                  isContainer = "1";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  Position = "1 0";
                  Extent = "156 16";
                  MinExtent = "16 16";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
               };
            };
         };
      };
   };
};

   


new GuiMouseEventCtrl(CameraSpeedDropdownCtrlContainer, EditorGuiGroup) {
   internalName = "AggregateControl";
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 8";
   visible = "1";
   helpTag = "0";
   class = "EditorDropdownSliderContainer";
   
   new GuiContainer(){
      position = firstWord(CameraSpeedDropdownContainer.position) + firstWord(EditorGuiToolbar.position) + -6 SPC 
            (getWord(CameraSpeedDropdownContainer, 1)) + 31;
      extent = "146 39";  
      isContainer = "1";
      Profile = "IconDropdownProfile";
      HorizSizing = "right";
      VertSizing = "bottom"; 
      
      new GuiBitmapCtrl(){ // Fast
         position = "105 15";
         extent = "2 8";
         bitmapAsset = "ToolsModule:separator_h_image";  
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "right";
         VertSizing = "bottom"; 
      };
      new GuiBitmapCtrl(){ // normal
         position = "73 15";
         extent = "2 8";
         bitmapAsset = "ToolsModule:separator_h_image";  
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "right";
         VertSizing = "bottom"; 
      };
      new GuiBitmapCtrl(){ // slow
         position = "41 15";
         extent = "2 8";
         bitmapAsset = "ToolsModule:separator_h_image";  
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "right";
         VertSizing = "bottom"; 
      };
         
         new GuiSliderCtrl(){ //camera speed slider
            internalName = "slider";
            position = "9 17";
            extent = "129 15";
            bitmap = "tools/gui/images/separator-h.png";  
            HorizSizing = "width";
            VertSizing = "bottom"; 
            range = "1 200";
            ticks = "0";
            value = "100";
            AltCommand = "EWorldEditorCameraSpeed.updateMenuBar( $ThisControl );";
         };
         new GuiTextCtrl(){ // Normal
            internalName = "text";
            position = "54 3";
            extent = "39 18";
            text = "Normal";  
            Profile = "ToolsGuiTextCenterProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
         };
      new GuiTextCtrl(){ // -
         position = "11 2";
         extent = "39 18";
         text = "-";  
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
      };
      new GuiTextCtrl(){ // +
         position = "98 5";
         extent = "39 13";
         text = "+";  
         Profile = "ToolsGuiTextRightProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
      };
   };
};
//--- OBJECT WRITE END ---
