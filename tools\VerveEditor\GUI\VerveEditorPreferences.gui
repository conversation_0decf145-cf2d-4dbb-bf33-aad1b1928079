//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(VerveEditorPreferenceGui) {
   canSaveDynamicFields = "0";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";

   new GuiWindowCtrl() {
      canSaveDynamicFields = "0";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      HorizSizing = "center";
      VertSizing = "center";
      position = "392 253";
      Extent = "240 262";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      Docking = "None";
      Margin = "4 24 4 4";
      Padding = "4 24 4 4";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      resizeWidth = "0";
      resizeHeight = "0";
      canMove = "0";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "50 50";
      closeCommand = "VerveEditor::CloseEditorPreferences();";
      EdgeSnap = "1";
      canCollapse = "0";
      CollapseGroup = "-1";
      CollapseGroupNum = "-1";
      text = "Verve Editor - Preferences";

      new GuiControl() {
         canSaveDynamicFields = "0";
         isContainer = "1";
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "10 93";
         Extent = "220 125";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";

         new GuiBitmapBorderCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiBitmapBorderProfile";
            HorizSizing = "width";
            VertSizing = "height";
            position = "0 10";
            Extent = "221 116";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
         };
         new GuiTextEditCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            class = "VerveEditorPreferenceLabel";
            Profile = "VEditorPreferenceLabelProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "10 2";
            Extent = "66 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = " Event Snap ";
            maxLength = "1024";
         };
         new GuiTextCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "30 50";
            Extent = "110 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "Threshold:";
            maxLength = "1024";
         };
         new GuiCheckBoxCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiCheckBoxProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "6 30";
            Extent = "128 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Variable = "$Pref::VerveEditor::Event::SnapToTime";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            text = "   Snap to Time";
            groupNum = "-1";
            buttonType = "ToggleButton";
            useMouseEvents = "0";
            useInactiveState = "0";
         };
         new GuiTextEditCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiTextEditProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "150 50";
            Extent = "64 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Variable = "$Pref::VerveEditor::Event::SnapToTimeThreshold";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            maxLength = "1024";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
            passwordMask = "*";
         };
         new GuiCheckBoxCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiCheckBoxProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "6 80";
            Extent = "128 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Variable = "$Pref::VerveEditor::Event::SnapToSiblings";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            text = "   Snap to Siblings";
            groupNum = "-1";
            buttonType = "ToggleButton";
            useMouseEvents = "0";
            useInactiveState = "0";
         };
         new GuiTextCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "30 100";
            Extent = "110 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "Threshold:";
            maxLength = "1024";
         };
         new GuiTextEditCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiTextEditProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "150 100";
            Extent = "64 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Variable = "$Pref::VerveEditor::Event::SnapToSiblingThreshold";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            maxLength = "1024";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
            passwordMask = "*";
         };
      };
      new GuiControl() {
         canSaveDynamicFields = "0";
         isContainer = "1";
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "10 30";
         Extent = "220 53";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";

         new GuiBitmapBorderCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiBitmapBorderProfile";
            HorizSizing = "width";
            VertSizing = "height";
            position = "0 10";
            Extent = "221 44";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
         };
         new GuiTextEditCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            class = "VerveEditorPreferenceLabel";
            Profile = "VEditorPreferenceLabelProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "10 2";
            Extent = "70 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = " Recent Files ";
            maxLength = "1024";
         };
         new GuiTextEditCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiTextEditProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "6 30";
            Extent = "40 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Variable = "$Pref::VerveEditor::RecentFileSize";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            maxLength = "1024";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
            passwordMask = "*";
         };
         new GuiTextCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "56 30";
            Extent = "158 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "items shown in Window Menu";
            maxLength = "1024";
         };
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "151 228";
         Extent = "80 24";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "VerveEditor::CloseEditorPreferences();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         text = "OK";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
   };
};
//--- OBJECT WRITE END ---

function VerveEditorPreferenceLabel::onWake( %this )
{
    %this.setActive( false );
}