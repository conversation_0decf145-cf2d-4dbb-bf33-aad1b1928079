$guiContent = new GuiControl(ManageBookmarksContainer, EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiModelessDialogProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "800 600";
   MinExtent = "8 2";
   canSave = "1";
   isDecoy = "0";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";

   new GuiWindowCollapseCtrl(EManageBookmarks) {
      internalName = "ManageBookmarksWindow";
      Enabled = "1";
      isContainer = "1";
      profile = "ToolsGuiWindowProfile";
      HorizSizing = "windowRelative";
      VertSizing = "windowRelative";
      resizeWidth = "1";
      resizeHeight = "1";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      position = "50 90";
      extent = "180 306";
      MinExtent = "120 130";
      text = "Camera Bookmark Manager";
      closeCommand = "EManageBookmarks.hideDialog();";
      EdgeSnap = "0";
      canCollapse = "0";
      visible = "0";
      Margin = "5 5 5 5";
      Padding = "5 5 5 5";
      
      new GuiControl(){
         horizSizing = "width";
         vertSizing = "bottom";
         position = "4 23";
         extent = "170 20";
         //Docking = "Top";
         
         new GuiTextCtrl() {
            profile = "ToolsGuiTextProfile";
            horizSizing = "right";
            vertSizing = "bottom";
            position = "1 2";
            extent = "24 16";
            minExtent = "8 8";
            visible = "1";
            setFirstResponder = "0";
            modal = "1";
            helpTag = "0";
            text = "New";
         };
         new GuiTextEditCtrl(EAddBookmarkWindowName) {
            profile = "ToolsGuiTextEditProfile";
            horizSizing = "width";
            vertSizing = "bottom";
            position = "27 2";
            extent = "126 18";
            minExtent = "8 8";
            visible = "1";
            setFirstResponder = "0";
            modal = "1";
            helpTag = "0";
            historySize = "0";
         };
         new GuiBitmapButtonCtrl(EAddBookmarkWindowOK) {
            profile = "ToolsGuiButtonProfile";
            horizSizing = "left";
            vertSizing = "bottom";
            position = "158 3";
            extent = "17 17";
            minExtent = "8 8";
            visible = "1";
            setFirstResponder = "0";
            modal = "1";
            command = "ManageBookmarksContainer.onOK();";
            bitmapAsset = "ToolsModule:new_n_image";
            helpTag = "0";
            text = "Create";
            tooltip = "Create New Camera Bookmark";
            accelerator = "return";
         };
      };
            
      new GuiScrollCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "ToolsGuiScrollProfile";
         HorizSizing = "width";
         VertSizing = "height";
         Position = "4 12";
         Extent = "300 200";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Docking = "Client";
         Margin = "26 1 3 3";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "true";
         lockVertScroll = "false";
         constantThumbHeight = "0";
         childMargin = "2 2";

         new GuiStackControl() {
            internalName = "ManageBookmarksWindowStack";
            StackingType = "Vertical";
            HorizStacking = "Left to Right";
            VertStacking = "Top to Bottom";
            Padding = "2";
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "1";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            Position = "0 0";
            Extent = "300 200";
            MinExtent = "16 16";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
         };
      };
   };
};
