//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(TerrainImportGui, EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiOverlayProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "800 600";
   MinExtent = "8 2";
   canSave = "1";
   isDecoy = "0";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";

   new GuiWindowCtrl() {
      canSaveDynamicFields = "0";
      internalName = "TerrainImport";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      HorizSizing = "center";
      VertSizing = "center";
      Position = "119 84";
      Extent = "391 257";
      MinExtent = "391 257";
      canSave = "1";
      isDecoy = "0";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "1";
      AnchorLeft = "1";
      AnchorRight = "1";
      resizeWidth = "1";
      resizeHeight = "0";
      canMove = "1";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "4 4";
      closeCommand = "Canvas.popDialog( TerrainImportGui );";
      EdgeSnap = "0";
      text = "Import Terrain Height Map";

      new GuiTextEditCtrl() {
         canSaveDynamicFields = "0";
         internalName = "HeightfieldFilename";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextEditProfile";
         HorizSizing = "width";
         VertSizing = "top";
         Position = "10 85";
         Extent = "298 18";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = " ";
         maxLength = "1024";
         historySize = "0";
         password = "0";
         tabComplete = "0";
         sinkAllKeyEvents = "0";
         passwordMask = "*";
      };
      new GuiTextCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "right";
         VertSizing = "top";
         Position = "11 66";
         Extent = "120 20";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "Height Map Image:";
         maxLength = "1024";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "316 83";
         Extent = "65 22";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         Command = "TerrainImportGui.browseForHeightfield();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         text = "Browse...";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiTextEditCtrl() {
         canSaveDynamicFields = "0";
         internalName = "MetersPerPixel";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextEditProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "226 44";
         Extent = "82 18";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "1";
         maxLength = "1024";
         historySize = "0";
         password = "0";
         tabComplete = "0";
         sinkAllKeyEvents = "0";
         passwordMask = "*";
      };
      new GuiTextCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "226 26";
         Extent = "88 18";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "Meters Per Pixel";
         maxLength = "1024";
      };
      new GuiTextCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "316 26";
         Extent = "64 18";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "Height Scale:";
         maxLength = "1024";
      };
      new GuiTextEditCtrl() {
         canSaveDynamicFields = "0";
         internalName = "HeightScale";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextEditProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "316 44";
         Extent = "64 18";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "256";
         maxLength = "1024";
         historySize = "0";
         password = "0";
         tabComplete = "0";
         sinkAllKeyEvents = "0";
         passwordMask = "*";
      };
      new GuiBitmapCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "width";
         VertSizing = "bottom";
         Position = "10 112";
         Extent = "365 2";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         bitmapAsset = "ToolsModule:separator_v_image";
         wrap = "0";
      };
      new GuiTextCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "right";
         VertSizing = "top";
         Position = "14 123";
         Extent = "74 18";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "1";
         text = "Texture Map";
         maxLength = "1024";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "341 142";
         Extent = "18 18";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         Command = "TerrainImportGui.browseForOpacityMap();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         text = "+";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "341 165";
         Extent = "18 18";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         Command = "TerrainImportGui.removeOpacitymap();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         text = "-";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "195 225";
         Extent = "88 24";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         Command = "TerrainImportGui.acceptSettings();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         text = "Accept";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiScrollCtrl() {
         canSaveDynamicFields = "0";
         internalName = "OpacityLayerScroll";
         Enabled = "1";
         isContainer = "1";
         Profile = "ToolsGuiScrollProfile";
         HorizSizing = "width";
         VertSizing = "top";
         Position = "10 142";
         Extent = "326 75";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "true";
         lockVertScroll = "false";
         constantThumbHeight = "0";
         childMargin = "0 0";

         new GuiTextListCtrl() {
            canSaveDynamicFields = "0";
            internalName = "OpacityLayerTextList";
            Enabled = "1";
            isContainer = "1";
            Profile = "ToolsGuiTextListProfile";
            HorizSizing = "width";
            VertSizing = "top";
            Position = "1 1";
            Extent = "293 2";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            AltCommand = "TerrainImportGui.onOpacityListDblClick();";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            enumerate = "0";
            resizeCell = "1";
            columns = "0 250 300";
            fitParentWidth = "1";
            clipColumnText = "1";
         };
      };
      new GuiTextCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         Position = "264 123";
         Extent = "48 18";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "Channels";
         maxLength = "1024";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "341 199";
         Extent = "40 18";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         Command = "TerrainImportGui.onOpacityListDblClick();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         text = "Edit";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "293 225";
         Extent = "88 24";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         Command = "TerrainImportGui.cancel();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         text = "Cancel";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiCheckBoxCtrl() {
         text = " Flip Y axis?";
         groupNum = "-1";
         buttonType = "ToggleButton";
         useMouseEvents = "0";
         position = "12 222";
         extent = "140 30";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         profile = "ToolsGuiCheckBoxProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "GuiToolTipProfile";
         hovertime = "1000";
         isContainer = "0";
         internalName = "FlipYAxis";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
   };
};
//--- OBJECT WRITE END ---


function TerrainImportGui::onWake( %this )
{   
   if ( !isObject( %this.namesArray ) )
      %this.namesArray = new ArrayObject();

   if ( !isObject( %this.channelsArray ) )
      %this.channelsArray = new ArrayObject();
}

function TerrainImportGui::acceptSettings( %this )
{
   // Gather all the import settings.
   AssetBrowser.newAssetSettings.importingTerrain = true;
   AssetBrowser.newAssetSettings.heightMapPng = %this-->HeightfieldFilename.getText();
     
   AssetBrowser.newAssetSettings.metersPerPixel = %this-->MetersPerPixel.getText();
   AssetBrowser.newAssetSettings.heightScale = %this-->HeightScale.getText();
   
   AssetBrowser.newAssetSettings.flipYAxis = %this-->FlipYAxis.isStateOn();
   
   // Grab and validate terrain object name.
   
   AssetBrowser.newAssetSettings.opacityNames = "";
   AssetBrowser.newAssetSettings.materialNames = "";
   
   AssetBrowser.newAssetSettings.opacityList = %this-->OpacityLayerTextList;
   
   %opacityList = %this-->OpacityLayerTextList;

   for( %i = 0; %i < %opacityList.rowCount(); %i++ )
   {
      %itemText = %opacityList.getRowTextById( %i );
      %opacityName = %this.namesArray.getValue( %i );
     
      %channelInfo = %this.channelsArray.getValue( %i );
      %channel = getWord( %channelInfo, 0 );
      
      %materialName = getField( %itemText, 2 );

      AssetBrowser.newAssetSettings.opacityNames = AssetBrowser.newAssetSettings.opacityNames @ %opacityName TAB %channel @ "\n";
      AssetBrowser.newAssetSettings.materialNames = AssetBrowser.newAssetSettings.materialNames @ %materialName @ "\n";
   }

   //AssetBrowser.newAssetSettings.updated = nameToID( %terrainName );
   
   Canvas.popDialog( %this );
}

function TerrainImportGui::cancel( %this )
{
   AssetBrowser.newAssetSettings.importingTerrain = false;
   Canvas.popDialog( TerrainImportGui );
}

function TerrainImportGui::doOpenDialog( %this, %filter, %callback )
{
   // TODO
   %currentFile = "";

   %dlg = new OpenFileDialog()
   {
      Filters = %filter;
      DefaultFile = %currentFile;
      ChangePath = false;
      MustExist = true;
      MultipleFiles = false;
   };
   
   if(filePath( %currentFile ) !$= "")
      %dlg.DefaultPath = filePath(%currentFile);
   else
      %dlg.DefaultPath = getMainDotCSDir();
      
   if(%dlg.Execute())
      eval(%callback @ "(\"" @ %dlg.FileName @ "\");");
   
   
   %dlg.delete();
}

function TerrainImportGui_SetHeightfield( %name )
{
   TerrainImportGui-->HeightfieldFilename.setText( %name );
}

$TerrainImportGui::HeightFieldFilter = "Heightfield Files (*.png, *.bmp, *.jpg, *.gif)|*.png;*.bmp;*.jpg;*.gif|All Files (*.*)|*.*|";
$TerrainImportGui::OpacityMapFilter = "Opacity Map Files (*.png, *.bmp, *.jpg, *.gif)|*.png;*.bmp;*.jpg;*.gif|All Files (*.*)|*.*|";

function TerrainImportGui::browseForHeightfield( %this )
{
   %this.doOpenDialog( $TerrainImportGui::HeightFieldFilter, "TerrainImportGui_SetHeightfield" );
}

function TerrainImportGuiAddOpacityMap( %name )
{
   // TODO: Need to actually look at
   // the file here and figure
   // out how many channels it has.

   %txt = makeRelativePath( %name, getWorkingDirectory() );   
   
   // Will need to do this stuff
   // once per channel in the file
   // currently it works with just grayscale.   
   %channelsTxt = "R" TAB "G" TAB "B" TAB "A";
   %bitmapInfo = getBitmapinfo( makeFullPath(%name) );
   
   %channelCount = getWord( %bitmapInfo, 2 );   
   
   %opacityList = TerrainImportGui-->OpacityLayerTextList;

   for ( %i = 0; %i < %channelCount; %i++ )
   {
      TerrainImportGui.namesArray.push_back( %txt, %name );
      TerrainImportGui.channelsArray.push_back( %txt, getWord( %channelsTxt, %i ) TAB %channelCount );

      //TerrainImportGui.namesArray.echo();   
   
      %count = %opacityList.rowCount();
      %opacityList.addRow( %count, %txt TAB getWord( %channelsTxt, %i ) );
   }
   
   //OpacityMapListBox.addItem( %name );
}

function TerrainImportGui::browseForOpacityMap( %this )
{
   TerrainImportGui.doOpenDialog( $TerrainImportGui::OpacityMapFilter, "TerrainImportGuiAddOpacityMap" );
}

function TerrainImportGui::removeOpacitymap( %this )
{   
   %opacityList = %this-->OpacityLayerTextList;
   
   //%itemIdx = OpacityMapListBox.getSelectedItem();   
   %itemIdx = %opacityList.getSelectedId();
   if ( %itemIdx < 0 ) // -1 is no item selected
      return;
  
   %this.namesArray.erase( %itemIdx );  
   %this.channelsArray.erase( %itemIdx );
  
   //%this.namesArray.echo();  
  
   %opacityList.removeRowById( %itemIdx );
      
   //OpacityMapListBox.deleteItem( %itemIdx );
}

function TerrainImportGui::onOpacityListDblClick( %this )
{
   %opacityList = %this-->OpacityLayerTextList;
   
   //echo( "Double clicked the opacity list control!" );
   %itemIdx = %opacityList.getSelectedId();
   if ( %itemIdx < 0 )
      return;

   %this.activeIdx = %itemIdx;
   
   %rowTxt = %opacityList.getRowTextById( %itemIdx );
   %matTxt = getField( %rowTxt, 2 );
   %matId = getField( %rowTxt, 3 );
         
   TerrainMaterialDlg.showByObjectId( %matId, TerrainImportGui_TerrainMaterialApplyCallback );
}

function TerrainImportGui_TerrainMaterialApplyCallback( %mat, %matIndex )
{           
   // Skip over a bad selection.
   if ( !isObject( %mat ) )   
      return;
                        
   %opacityList = TerrainImportGui-->OpacityLayerTextList;
                           
   %itemIdx = TerrainImportGui.activeIdx;
   
   if ( %itemIdx < 0 || %itemIdx $= "" )
      return;

   %rowTxt = %opacityList.getRowTextById( %itemIdx );
   
   %columntTxtCount = getFieldCount( %rowTxt );
   if ( %columntTxtCount > 2 )
      %rowTxt = getFields( %rowTxt, 0, 1 );

   %opacityList.setRowById( %itemIdx, %rowTxt TAB %mat.internalName TAB %mat );
}
