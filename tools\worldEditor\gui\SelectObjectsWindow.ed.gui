//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(ESelectObjectsWindowContainer,EditorGuiGroup) {
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 2";
   horizSizing = "right";
   vertSizing = "bottom";
   fixedAspectRatio = "0";
   profile = "ToolsGuiDefaultProfile";
   visible = "1";
   active = "1";
   tooltipProfile = "ToolsGuiToolTipProfile";
   hovertime = "1000";
   isContainer = "1";
   canSave = "1";
   canSaveDynamicFields = "1";

   new GuiWindowCtrl(ESelectObjectsWindow) {
      text = "Select Objects";
      resizeWidth = "0";
      resizeHeight = "0";
      canMove = "1";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      canCollapse = "1";
      closeCommand = "$ThisControl.toggleVisibility();";
      edgeSnap = "1";
      margin = "0 0 0 0";
      padding = "0 0 0 0";
      anchorTop = "1";
      anchorBottom = "0";
      anchorLeft = "1";
      anchorRight = "0";
      position = "268 177";
      extent = "380 373";
      minExtent = "200 100";
      horizSizing = "right";
      vertSizing = "bottom";
      fixedAspectRatio = "0";
      profile = "ToolsGuiWindowProfile";
      visible = "1";
      active = "1";
      tooltipProfile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      isContainer = "1";
      canSave = "1";
      canSaveDynamicFields = "0";
      class = "EObjectSelection";
      internalName = "SelectObjectsWindow";

      new GuiBitmapBorderCtrl() {
         position = "7 104";
         extent = "265 262";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         fixedAspectRatio = "0";
         profile = "ToolsGuiGroupBorderProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         isContainer = "1";
         canSave = "1";
         canSaveDynamicFields = "0";

         new GuiScrollCtrl() {
            willFirstRespond = "1";
            hScrollBar = "alwaysOff";
            vScrollBar = "dynamic";
            lockHorizScroll = "0";
            lockVertScroll = "0";
            constantThumbHeight = "0";
            childMargin = "0 0";
            mouseWheelScrollSpeed = "-1";
            margin = "0 0 0 0";
            padding = "0 0 0 0";
            anchorTop = "1";
            anchorBottom = "0";
            anchorLeft = "1";
            anchorRight = "0";
            position = "10 25";
            extent = "246 200";
            minExtent = "8 2";
            horizSizing = "right";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiScrollProfile";
            visible = "1";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "1";
            canSave = "1";
            canSaveDynamicFields = "0";

            new GuiStackControl() {
               stackingType = "Vertical";
               horizStacking = "Left to Right";
               vertStacking = "Top to Bottom";
               padding = "0";
               dynamicSize = "1";
               changeChildSizeToFit = "1";
               changeChildPosition = "1";
               position = "1 1";
               extent = "246 1242";
               minExtent = "16 16";
               horizSizing = "width";
               vertSizing = "bottom";
               fixedAspectRatio = "0";
               profile = "ToolsGuiDefaultProfile";
               visible = "1";
               active = "1";
               tooltipProfile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               isContainer = "1";
               internalName = "classList";
               canSave = "1";
               canSaveDynamicFields = "0";

            };
         };
         new GuiButtonCtrl() {
            text = "Select All";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            position = "10 231";
            extent = "65 20";
            minExtent = "8 2";
            horizSizing = "right";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiButtonProfile";
            visible = "1";
            active = "1";
            command = "ESelectObjectsWindow.selectAllInClassList( true );";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
         new GuiTextCtrl() {
            text = "Classes";
            maxLength = "1024";
            margin = "0 0 0 0";
            padding = "0 0 0 0";
            anchorTop = "1";
            anchorBottom = "0";
            anchorLeft = "1";
            anchorRight = "0";
            position = "113 6";
            extent = "40 17";
            minExtent = "8 2";
            horizSizing = "right";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiAutoSizeTextProfile";
            visible = "1";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
         new GuiButtonCtrl() {
            text = "Deselect All";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            position = "76 231";
            extent = "65 20";
            minExtent = "8 2";
            horizSizing = "right";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiButtonProfile";
            visible = "1";
            active = "1";
            command = "ESelectObjectsWindow.selectAllInClassList( false );";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
      };
      new GuiBitmapBorderCtrl() {
         position = "7 25";
         extent = "366 74";
         minExtent = "8 2";
         horizSizing = "width";
         vertSizing = "bottom";
         fixedAspectRatio = "0";
         profile = "ToolsGuiGroupBorderProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         isContainer = "1";
         canSave = "1";
         canSaveDynamicFields = "0";

         new GuiTextCtrl() {
            text = "Name Pattern";
            maxLength = "1024";
            margin = "0 0 0 0";
            padding = "0 0 0 0";
            anchorTop = "1";
            anchorBottom = "0";
            anchorLeft = "1";
            anchorRight = "0";
            position = "11 9";
            extent = "67 17";
            minExtent = "8 2";
            horizSizing = "right";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiAutoSizeTextProfile";
            visible = "1";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
         new GuiCheckBoxCtrl() {
            useInactiveState = "0";
            text = "Retain Current Selection";
            groupNum = "-1";
            buttonType = "ToggleButton";
            useMouseEvents = "0";
            position = "216 46";
            extent = "140 30";
            minExtent = "8 2";
            horizSizing = "right";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiCheckBoxProfile";
            visible = "1";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            internalName = "retainSelection";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
         new GuiCheckBoxCtrl() {
            useInactiveState = "0";
            text = "Create Selection Set";
            groupNum = "-1";
            buttonType = "ToggleButton";
            useMouseEvents = "0";
            position = "13 73";
            extent = "117 30";
            minExtent = "8 2";
            horizSizing = "right";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiCheckBoxProfile";
            visible = "0";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            internalName = "createSelectionSet";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
         new GuiTextEditCtrl() {
            historySize = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
            password = "0";
            passwordMask = "•";
            maxLength = "1024";
            margin = "0 0 0 0";
            padding = "0 0 0 0";
            anchorTop = "1";
            anchorBottom = "0";
            anchorLeft = "1";
            anchorRight = "0";
            position = "157 80";
            extent = "199 17";
            minExtent = "20 2";
            horizSizing = "width";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiTextEditProfile";
            visible = "0";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            internalName = "selectionSetName";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
         new GuiTextEditCtrl() {
            historySize = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
            password = "0";
            passwordMask = "•";
            maxLength = "1024";
            margin = "0 0 0 0";
            padding = "0 0 0 0";
            anchorTop = "1";
            anchorBottom = "0";
            anchorLeft = "1";
            anchorRight = "0";
            position = "91 9";
            extent = "265 17";
            minExtent = "20 2";
            horizSizing = "width";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiTextEditProfile";
            visible = "1";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            internalName = "namePattern";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
         new GuiTextCtrl() {
            text = "Select Objects in Group";
            maxLength = "1024";
            margin = "0 0 0 0";
            padding = "0 0 0 0";
            anchorTop = "1";
            anchorBottom = "0";
            anchorLeft = "1";
            anchorRight = "0";
            position = "11 30";
            extent = "116 17";
            minExtent = "8 2";
            horizSizing = "right";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiAutoSizeTextProfile";
            visible = "1";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
         new GuiPopUpMenuCtrl() {
            maxPopupHeight = "200";
            sbUsesNAColor = "0";
            reverseTextList = "0";
            bitmapBounds = "16 16";
            maxLength = "1024";
            margin = "0 0 0 0";
            padding = "0 0 0 0";
            anchorTop = "1";
            anchorBottom = "0";
            anchorLeft = "1";
            anchorRight = "0";
            position = "138 30";
            extent = "218 17";
            minExtent = "20 2";
            horizSizing = "width";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiPopUpMenuProfile";
            visible = "1";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            internalName = "groupList";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
      };
      new GuiBitmapBorderCtrl() {
         position = "246 104";
         extent = "233 262";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         fixedAspectRatio = "0";
         profile = "ToolsGuiGroupBorderProfile";
         visible = "0";
         active = "1";
         tooltipProfile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         isContainer = "1";
         canSave = "1";
         canSaveDynamicFields = "0";

         new GuiScrollCtrl() {
            willFirstRespond = "1";
            hScrollBar = "alwaysOff";
            vScrollBar = "dynamic";
            lockHorizScroll = "0";
            lockVertScroll = "0";
            constantThumbHeight = "0";
            childMargin = "0 0";
            mouseWheelScrollSpeed = "-1";
            margin = "0 0 0 0";
            padding = "0 0 0 0";
            anchorTop = "1";
            anchorBottom = "0";
            anchorLeft = "1";
            anchorRight = "0";
            position = "9 25";
            extent = "215 200";
            minExtent = "8 2";
            horizSizing = "right";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiScrollProfile";
            visible = "1";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "1";
            canSave = "1";
            canSaveDynamicFields = "0";

            new GuiStackControl() {
               stackingType = "Vertical";
               horizStacking = "Left to Right";
               vertStacking = "Top to Bottom";
               padding = "0";
               dynamicSize = "1";
               changeChildSizeToFit = "1";
               changeChildPosition = "1";
               position = "1 1";
               extent = "215 16";
               minExtent = "16 16";
               horizSizing = "width";
               vertSizing = "bottom";
               fixedAspectRatio = "0";
               profile = "ToolsGuiDefaultProfile";
               visible = "1";
               active = "1";
               tooltipProfile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               isContainer = "1";
               internalName = "filterList";
               canSave = "1";
               canSaveDynamicFields = "0";
            };
         };
         new GuiButtonCtrl() {
            text = "Select All";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            position = "9 231";
            extent = "65 20";
            minExtent = "8 2";
            horizSizing = "right";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiButtonProfile";
            visible = "1";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
         new GuiTextCtrl() {
            text = "Filters";
            maxLength = "1024";
            margin = "0 0 0 0";
            padding = "0 0 0 0";
            anchorTop = "1";
            anchorBottom = "0";
            anchorLeft = "1";
            anchorRight = "0";
            position = "101 6";
            extent = "30 17";
            minExtent = "8 2";
            horizSizing = "right";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiAutoSizeTextProfile";
            visible = "1";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
         new GuiButtonCtrl() {
            text = "Deselect All";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            position = "75 231";
            extent = "65 20";
            minExtent = "8 2";
            horizSizing = "right";
            vertSizing = "bottom";
            fixedAspectRatio = "0";
            profile = "ToolsGuiButtonProfile";
            visible = "1";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "0";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
      };
      new GuiButtonCtrl() {
         text = "Select";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
         position = "278 104";
         extent = "95 30";
         minExtent = "8 2";
         horizSizing = "left";
         vertSizing = "bottom";
         fixedAspectRatio = "0";
         profile = "ToolsGuiButtonProfile";
         visible = "1";
         active = "1";
         command = "ESelectObjectsWindow.onSelectObjects(true);";
         tooltipProfile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         isContainer = "0";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
      new GuiButtonCtrl() {
         text = "Deselect";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
         position = "278 137";
         extent = "95 30";
         minExtent = "8 2";
         horizSizing = "left";
         vertSizing = "bottom";
         fixedAspectRatio = "0";
         profile = "ToolsGuiButtonProfile";
         visible = "1";
         active = "1";
         command = "ESelectObjectsWindow.onSelectObjects(false);";
         tooltipProfile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         isContainer = "0";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
   };
};
//--- OBJECT WRITE END ---
