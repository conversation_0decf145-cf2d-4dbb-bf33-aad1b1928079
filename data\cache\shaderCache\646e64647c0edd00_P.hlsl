//*****************************************************************************
// Torque -- HLSL procedural shader
//*****************************************************************************

// Dependencies:
#include "core/rendering/shaders/torque.hlsl"

// Features:
// Vert Position
// Background Object
// Base Texture
// Diffuse Vertex Color
// Deferred Shading: PBR Config Explicit Numbers
// Deferred Shading: Mat Info Flags
// Fog
// Debug Viz
// HDR Output
// Translucent
// Forward Shaded Material

struct ConnectData
{
   float4 vpos            : SV_Position;
   float4 vertColor       : COLOR;
   float2 texCoord        : TEXCOORD0;
   float3 wsPosition      : TEXCOORD1;
};


struct Fragout
{
   float4 col : SV_Target0;
};


//-----------------------------------------------------------------------------
// Main
//-----------------------------------------------------------------------------
Fragout main( ConnectData IN,
              uniform SamplerState diffuseMap      : register(S0),
              uniform Texture2D diffuseMapTex   : register(T0),
              uniform float     metalness       : register(C0),
              uniform float     roughness       : register(C1),
              uniform float     matInfoFlags    : register(C2),
              uniform float4    fogColor        : register(C3),
              uniform float3    eyePosWorld     : register(C4),
              uniform float3    fogData         : register(C5)
)
{
   Fragout OUT;

   // Vert Position
   
   // Background Object
   
   // Base Texture
float4 diffuseColor = diffuseMapTex.Sample(diffuseMap, IN.texCoord);
   OUT.col = diffuseColor;
   
   // Diffuse Vertex Color
   OUT.col *= IN.vertColor;
   
   // Deferred Shading: PBR Config Explicit Numbers
   float4 ORMConfig;
   ORMConfig.g = 1.0;
   ORMConfig.b = roughness;
   ORMConfig.a = metalness;
   
   // Deferred Shading: Mat Info Flags
   ORMConfig.r = matInfoFlags;
   
   // Fog
   float fogAmount = saturate( computeSceneFog( eyePosWorld, IN.wsPosition, fogData.r, fogData.g, fogData.b ) );
   OUT.col.rgb = lerp( fogColor.rgb, OUT.col.rgb, fogAmount );
   
   // Debug Viz
   
   // HDR Output
   OUT.col = hdrEncode( OUT.col );
   
   // Translucent
   
   // Forward Shaded Material
   

   return OUT;
}
