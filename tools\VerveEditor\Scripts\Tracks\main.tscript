//-----------------------------------------------------------------------------
// Verve
// Copyright (C) - Violent Tulip
//-----------------------------------------------------------------------------

function VerveEditor::InitTrackScripts()
{
    // Core.
    exec( "./VTrack." @ $TorqueScriptFileExtension );
    
    // Built-In.
    exec( "./VCameraShakeTrack." @ $TorqueScriptFileExtension );
    exec( "./VDirectorTrack." @ $TorqueScriptFileExtension );
    exec( "./VFadeTrack." @ $TorqueScriptFileExtension );
    exec( "./VLightObjectAnimationTrack." @ $TorqueScriptFileExtension );
    exec( "./VLightObjectToggleTrack." @ $TorqueScriptFileExtension );
    exec( "./VMotionTrack." @ $TorqueScriptFileExtension );
    exec( "./VParticleEffectToggleTrack." @ $TorqueScriptFileExtension );
    exec( "./VPostEffectToggleTrack." @ $TorqueScriptFileExtension );
    exec( "./VSceneJumpTrack." @ $TorqueScriptFileExtension );
    exec( "./VScriptEventTrack." @ $TorqueScriptFileExtension );
    exec( "./VShapeAnimationTrack." @ $TorqueScriptFileExtension );
    exec( "./VSlowMoTrack." @ $TorqueScriptFileExtension );
    exec( "./VSoundEffectTrack." @ $TorqueScriptFileExtension );
    exec( "./VSpawnSphereSpawnTargetTrack." @ $TorqueScriptFileExtension );
    
    // Custom.
    // Exec Custom Track Scripts.
    
    // Non-Unique Group List.
    $VerveEditor::NonUniqueTrackList = "VTrack VPostEffectToggleTrack VSoundEffectTrack";
}
VerveEditor::InitTrackScripts();
