//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(GenericPromptDialog) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiOverlayProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";

   new GuiWindowCtrl() {
      canSaveDynamicFields = "0";
      internalName = "GenericPromptWindow";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      HorizSizing = "center";
      VertSizing = "center";
      Position = "336 337";
      Extent = "352 93";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "0";
      AnchorBottom = "0";
      AnchorLeft = "0";
      AnchorRight = "0";
      resizeWidth = "0";
      resizeHeight = "0";
      canMove = "1";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "50 50";
      EdgeSnap = "1";
      text = "Error";
      closeCommand = "Canvas.popDialog(GenericPromptDialog);";

      new GuiTextCtrl() {
         canSaveDynamicFields = "0";
         internalName = "GenericPromptText";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiProgressTextProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "13 33";
         Extent = "328 15";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "Cannot use the Terrain Editor without a terrain";
         maxLength = "1024";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "center";
         VertSizing = "top";
         Position = "134 61";
         Extent = "88 24";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "Canvas.popDialog(GenericPromptDialog);";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         text = "OK";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "center";
         VertSizing = "top";
         Position = "230 61";
         Extent = "88 24";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "0";
         Command = "Canvas.popDialog(GenericPromptDialog);";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         text = "Cancel";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
   };
};
//--- OBJECT WRITE END ---
