//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiContainer(EditorChooseLevelGui, EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "GuiContentProfile";
   HorizSizing = "width";
   VertSizing = "height";
   Position = "0 0";
   Extent = "800 600";
   MinExtent = "8 8";
   canSave = "1";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";

   new GuiChunkedBitmapCtrl() {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "GuiContentProfile";
      HorizSizing = "width";
      VertSizing = "height";
      Position = "0 0";
      Extent = "800 600";
      MinExtent = "8 8";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      bitmap = "data/ui/images/background.png";
      useVariable = "0";
      tile = "0";
   };
};

$guiContent = new GuiContainer(EditorChooseLevelContainer, EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "GuiContentProfile";
   HorizSizing = "width";
   VertSizing = "height";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 8";
   canSave = "1";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";
   
   new GuiWindowCtrl(EditorChooseLevelWindow) {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      HorizSizing = "center";
      VertSizing = "center";
      Position = "416 187";
      Extent = "192 393";
      MinExtent = "8 8";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      resizeWidth = "0";
      resizeHeight = "0";
      canMove = "1";
      canClose = "0";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "50 50";
      EdgeSnap = "1";
      text = "Level Selector";

      new GuiTextCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "10 21";
         Extent = "171 18";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "1: Edit an Existing Level";
         maxLength = "255";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "42 360";
         Extent = "107 23";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Command = "WE_ReturnToMainMenu();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         text = "Play Game";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiScrollCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "ToolsGuiScrollProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "10 38";
         Extent = "171 194";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         willFirstRespond = "1";
         hScrollBar = "dynamic";
         vScrollBar = "dynamic";
         lockHorizScroll = false;
         lockVertScroll = "false";
         constantThumbHeight = "0";
         childMargin = "4 0";

         new GuiMLTextCtrl(WE_LevelList) {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiMLTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 1";
            Extent = "148 70";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            lineSpacing = "2";
            allowColorChars = "0";
            maxChars = "-1";
            useURLMouseCursor = "1";
         };
      };
      new GuiScrollCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "ToolsGuiScrollProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "10 250";
         Extent = "171 87";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         willFirstRespond = "1";
         hScrollBar = "dynamic";
         vScrollBar = "dynamic";
         lockHorizScroll = false;
         lockVertScroll = "false";
         constantThumbHeight = "0";
         childMargin = "4 0";

         new GuiMLTextCtrl(WE_TemplateList) {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiMLTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 1";
            Extent = "148 14";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            lineSpacing = "2";
            allowColorChars = "0";
            maxChars = "-1";
            useURLMouseCursor = "1";
         };
      };
      new GuiTextCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "10 232";
         Extent = "174 18";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "2: Create New from Template";
         maxLength = "255";
      };
      new GuiTextCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "10 338";
         Extent = "174 18";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "3: Play Game from Start";
         maxLength = "255";
      };
   };
};
//--- OBJECT WRITE END ---
