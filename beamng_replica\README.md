# BeamNG.drive Replica

A small-scale replica of BeamNG.drive built using Torque3D engine with JBEAM physics system.

## Project Structure

```
beamng_replica/
├── assets/                 # 3D models, textures, materials
│   ├── vehicles/          # Vehicle 3D models and textures
│   ├── maps/              # Terrain heightmaps and textures
│   └── materials/         # Material definitions
├── jbeam/                 # JBEAM physics definitions
│   ├── vehicles/          # Vehicle JBEAM files
│   └── parts/             # Individual part definitions
├── scripts/               # TorqueScript game logic
│   ├── client/            # Client-side scripts
│   ├── server/            # Server-side scripts
│   └── shared/            # Shared scripts
├── cpp/                   # C++ engine extensions
│   ├── jbeam_parser/      # JBEAM file parser
│   ├── physics/           # Soft-body physics implementation
│   └── vehicle_system/    # Vehicle management system
└── levels/                # Game levels/maps
    └── test_track/        # Basic test track
```

## Technology Stack

- **Engine**: Torque3D (open source)
- **Physics**: Custom JBEAM-based soft-body system
- **Languages**: C++, TorqueScript, Lua
- **Assets**: Free 3D models and textures

## Features

- JBEAM-based vehicle physics
- Soft-body deformation simulation
- Node-beam physics system
- Vehicle part/slot system
- Basic driving mechanics
- Simple test environment

## Getting Started

1. Ensure Torque3D is properly installed
2. Compile the C++ extensions
3. Load the test level
4. Drive the sample vehicle

## JBEAM Format

JBEAM files use JSON format with C-style comments and optional trailing commas:

```json
{
    "vehicle_name": {
        "information": {
            "name": "Sample Vehicle",
            "authors": "BeamNG Replica Team"
        },
        "nodes": [
            // Node definitions with position and properties
            ["id", "posX", "posY", "posZ", {"nodeWeight": 25}]
        ],
        "beams": [
            // Beam connections between nodes
            ["node1", "node2", {"beamSpring": 4000000, "beamDamp": 500}]
        ]
    }
}
```
