//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(TimeAdjustGui, EditorGuiGroup) {
   isContainer = "1";
   Profile = "ToolsGuiModelessDialogProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";
   canSaveDynamicFields = "1";

   new GuiWindowCtrl() {
      resizeWidth = "0";
      resizeHeight = "0";
      canMove = "1";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "50 50";
      EdgeSnap = "1";
      text = "Time Adjust Gui";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = "338 63";
      Extent = "462 84";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      canSaveDynamicFields = "0";
      closeCommand = "Canvas.popDialog(TimeAdjustGui);";

      new GuiSliderCtrl(TimeAdjustSliderCtrl) {
         range = "0 1";
         ticks = "100";
         value = "0.1";
         isContainer = "0";
         Profile = "ToolsGuiSliderProfile";
         HorizSizing = "width";
         VertSizing = "bottom";
         position = "37 27";
         Extent = "389 19";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";
         altCommand = "$ThisControl.onAction();";
      };
      new GuiTextCtrl() {
         text = "Sunrise";
         maxLength = "1024";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         isContainer = "0";
         Profile = "ToolsGuiTextCenterProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "11 53";
         Extent = "59 19";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";
      };
      new GuiTextCtrl() {
         text = "Sunset";
         maxLength = "1024";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         isContainer = "0";
         Profile = "ToolsGuiTextCenterProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "206 53";
         Extent = "59 19";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";
      };
      new GuiTextCtrl() {
         text = "Noon";
         maxLength = "1024";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         isContainer = "0";
         Profile = "ToolsGuiTextCenterProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "108 53";
         Extent = "59 19";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";
      };
      new GuiTextCtrl() {
         text = "Midnight";
         maxLength = "1024";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         isContainer = "0";
         Profile = "ToolsGuiTextCenterProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "307 53";
         Extent = "59 19";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";
      };
      new GuiTextCtrl() {
         text = "Sunrise";
         maxLength = "1024";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         isContainer = "0";
         Profile = "ToolsGuiTextCenterProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "393 53"; 
         Extent = "59 19";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";
      };
   };
};
//--- OBJECT WRITE END ---

function TimeAdjustSliderCtrl::onAction(%this)
{
   // NOTE: Though this is a GuiControl which exists on the client-side
   // we access and modify the server-side TimeOfDay object. This is acceptible
   // because this is a "tools" gui which is not intended for a real-game
   // or multiplayer situation.
   
   if ( !isObject( %this.tod ) )
   {
      if ( isObject( getScene(0) ) )
      {
         for ( %i = 0; %i < getScene(0).getCount(); %i++ )
         {
            %obj = getScene(0).getObject( %i );
            
            if ( %obj.getClassName() $= "TimeOfDay" )
            {
               %this.tod = %obj;
               break;
            }            
         }
      }
   }
   
   if ( !isObject( %this.tod ) )
      return;
      
   %this.tod.time = %this.getValue();   
}

function toggleTimeAdjustGui()
{
   if ( TimeAdjustGui.isAwake() )
      Canvas.popDialog( TimeAdjustGui );
   else
      Canvas.pushDialog( TimeAdjustGui );
}