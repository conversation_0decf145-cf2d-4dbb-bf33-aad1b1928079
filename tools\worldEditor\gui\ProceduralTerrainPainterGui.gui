//--- OBJECT WRITE BEGIN ---  
$guiContent = new GuiControl(ProceduralTerrainPainterGui) {  
   canSaveDynamicFields = "0";  
   isContainer = "1";  
   Profile = "ToolsGuiDefaultProfile";  
   HorizSizing = "right";  
   VertSizing = "bottom";  
   Position = "0 0";  
   Extent = "1024 768";  
   MinExtent = "8 2";  
   canSave = "1";  
   Visible = "1";  
   tooltipprofile = "ToolsGuiToolTipProfile";  
   hovertime = "1000";  
  
   new GuiWindowCtrl(ProceduralTerrainPainterDescription) {  
      canSaveDynamicFields = "0";  
      isContainer = "1";  
      Profile = "ToolsGuiWindowProfile";  
      HorizSizing = "right";  
      VertSizing = "bottom";  
      Position = "285 83";  
      Extent = "175 233";  
      MinExtent = "8 2";  
      canSave = "1";  
      Visible = "1";  
      tooltipprofile = "ToolsGuiToolTipProfile";  
      hovertime = "1000";  
      Margin = "0 0 0 0";  
      Padding = "0 0 0 0";  
      AnchorTop = "1";  
      AnchorBottom = "0";  
      AnchorLeft = "1";  
      AnchorRight = "0";  
      resizeWidth = "0";  
      resizeHeight = "0";  
      canMove = "1";  
      canClose = "1";  
      canMinimize = "0";  
      canMaximize = "0";  
      minSize = "50 50";  
      EdgeSnap = "1";  
      canCollapse = "0";  
      CollapseGroup = "-1";  
      CollapseGroupNum = "-1";  
      closeCommand = "Canvas.popDialog(ProceduralTerrainPainterGui);";  
      text = "Generate layer mask";  
  
      new GuiButtonCtrl() {  
         canSaveDynamicFields = "0";  
         isContainer = "0";  
         Profile = "ToolsGuiButtonProfile";  
         HorizSizing = "right";  
         VertSizing = "bottom";  
         Position = "19 193";  
         Extent = "140 30";  
         MinExtent = "8 2";  
         canSave = "1";  
         Visible = "1";  
         Command = "generateProceduralTerrainMask();";  
         tooltipprofile = "ToolsGuiToolTipProfile";  
         hovertime = "1000";  
         text = "Generate";  
         groupNum = "-1";  
         buttonType = "PushButton";  
         useMouseEvents = "0";  
      };  
      new GuiTextCtrl() {  
         canSaveDynamicFields = "0";  
         isContainer = "0";  
         Profile = "ToolsGuiTextProfile";  
         HorizSizing = "right";  
         VertSizing = "bottom";  
         Position = "15 37";  
         Extent = "33 13";  
         MinExtent = "8 2";  
         canSave = "1";  
         Visible = "1";  
         tooltipprofile = "ToolsGuiToolTipProfile";  
         hovertime = "1000";  
         Margin = "0 0 0 0";  
         Padding = "0 0 0 0";  
         AnchorTop = "1";  
         AnchorBottom = "0";  
         AnchorLeft = "1";  
         AnchorRight = "0";  
         text = "HEIGHT";  
         maxLength = "1024";  
      };  
      new GuiTextCtrl() {  
         canSaveDynamicFields = "0";  
         isContainer = "0";  
         Profile = "ToolsGuiTextProfile";  
         HorizSizing = "right";  
         VertSizing = "bottom";  
         Position = "59 37";  
         Extent = "23 14";  
         MinExtent = "8 2";  
         canSave = "1";  
         Visible = "1";  
         tooltipprofile = "ToolsGuiToolTipProfile";  
         hovertime = "1000";  
         Margin = "0 0 0 0";  
         Padding = "0 0 0 0";  
         AnchorTop = "1";  
         AnchorBottom = "0";  
         AnchorLeft = "1";  
         AnchorRight = "0";  
         text = "Min.";  
         maxLength = "1024";  
      };  
      new GuiTextCtrl() {  
         canSaveDynamicFields = "0";  
         isContainer = "0";  
         Profile = "ToolsGuiTextProfile";  
         HorizSizing = "right";  
         VertSizing = "bottom";  
         Position = "59 62";  
         Extent = "23 14";  
         MinExtent = "8 2";  
         canSave = "1";  
         Visible = "1";  
         tooltipprofile = "ToolsGuiToolTipProfile";  
         hovertime = "1000";  
         Margin = "0 0 0 0";  
         Padding = "0 0 0 0";  
         AnchorTop = "1";  
         AnchorBottom = "0";  
         AnchorLeft = "1";  
         AnchorRight = "0";  
         text = "Max.";  
         maxLength = "1024";  
      };  
      new GuiTextEditCtrl() {  
         canSaveDynamicFields = "0";  
         isContainer = "0";  
         Profile = "ToolsGuiTextEditProfile";  
         HorizSizing = "right";  
         VertSizing = "bottom";  
         Position = "97 35";  
         Extent = "66 18";  
         MinExtent = "8 2";  
         canSave = "1";  
         Visible = "1";  
         Variable = "$TPPHeightMin";  
         tooltipprofile = "ToolsGuiToolTipProfile";  
         hovertime = "1000";  
         Margin = "0 0 0 0";  
         Padding = "0 0 0 0";  
         AnchorTop = "1";  
         AnchorBottom = "0";  
         AnchorLeft = "1";  
         AnchorRight = "0";  
         maxLength = "1024";  
         historySize = "0";  
         password = "0";  
         tabComplete = "0";  
         sinkAllKeyEvents = "0";  
         passwordMask = "*";  
      };  
      new GuiTextEditCtrl() {  
         canSaveDynamicFields = "0";  
         isContainer = "0";  
         Profile = "ToolsGuiTextEditProfile";  
         HorizSizing = "right";  
         VertSizing = "bottom";  
         Position = "97 60";  
         Extent = "66 18";  
         MinExtent = "8 2";  
         canSave = "1";  
         Visible = "1";  
         Variable = "$TPPHeightMax";  
         tooltipprofile = "ToolsGuiToolTipProfile";  
         hovertime = "1000";  
         Margin = "0 0 0 0";  
         Padding = "0 0 0 0";  
         AnchorTop = "1";  
         AnchorBottom = "0";  
         AnchorLeft = "1";  
         AnchorRight = "0";  
         maxLength = "1024";  
         historySize = "0";  
         password = "0";  
         tabComplete = "0";  
         sinkAllKeyEvents = "0";  
         passwordMask = "*";  
      };  
      new GuiTextCtrl() {  
         canSaveDynamicFields = "0";  
         isContainer = "0";  
         Profile = "ToolsGuiTextProfile";  
         HorizSizing = "right";  
         VertSizing = "bottom";  
         Position = "15 101";  
         Extent = "33 13";  
         MinExtent = "8 2";  
         canSave = "1";  
         Visible = "1";  
         tooltipprofile = "ToolsGuiToolTipProfile";  
         hovertime = "1000";  
         Margin = "0 0 0 0";  
         Padding = "0 0 0 0";  
         AnchorTop = "1";  
         AnchorBottom = "0";  
         AnchorLeft = "1";  
         AnchorRight = "0";  
         text = "SLOPE";  
         maxLength = "1024";  
      };  
      new GuiTextCtrl() {  
         canSaveDynamicFields = "0";  
         isContainer = "0";  
         Profile = "ToolsGuiTextProfile";  
         HorizSizing = "right";  
         VertSizing = "bottom";  
         Position = "59 101";  
         Extent = "23 14";  
         MinExtent = "8 2";  
         canSave = "1";  
         Visible = "1";  
         tooltipprofile = "ToolsGuiToolTipProfile";  
         hovertime = "1000";  
         Margin = "0 0 0 0";  
         Padding = "0 0 0 0";  
         AnchorTop = "1";  
         AnchorBottom = "0";  
         AnchorLeft = "1";  
         AnchorRight = "0";  
         text = "Min.";  
         maxLength = "1024";  
      };  
      new GuiTextCtrl() {  
         canSaveDynamicFields = "0";  
         isContainer = "0";  
         Profile = "ToolsGuiTextProfile";  
         HorizSizing = "right";  
         VertSizing = "bottom";  
         Position = "59 126";  
         Extent = "23 14";  
         MinExtent = "8 2";  
         canSave = "1";  
         Visible = "1";  
         tooltipprofile = "ToolsGuiToolTipProfile";  
         hovertime = "1000";  
         Margin = "0 0 0 0";  
         Padding = "0 0 0 0";  
         AnchorTop = "1";  
         AnchorBottom = "0";  
         AnchorLeft = "1";  
         AnchorRight = "0";  
         text = "Max.";  
         maxLength = "1024";  
      };  
      new GuiTextEditCtrl() {  
         canSaveDynamicFields = "0";  
         isContainer = "0";  
         Profile = "ToolsGuiTextEditProfile";  
         HorizSizing = "right";  
         VertSizing = "bottom";  
         Position = "97 99";  
         Extent = "66 18";  
         MinExtent = "8 2";  
         canSave = "1";  
         Visible = "1";  
         Variable = "$TPPSlopeMin";  
         tooltipprofile = "ToolsGuiToolTipProfile";  
         hovertime = "1000";  
         Margin = "0 0 0 0";  
         Padding = "0 0 0 0";  
         AnchorTop = "1";  
         AnchorBottom = "0";  
         AnchorLeft = "1";  
         AnchorRight = "0";  
         maxLength = "1024";  
         historySize = "0";  
         password = "0";  
         tabComplete = "0";  
         sinkAllKeyEvents = "0";  
         passwordMask = "*";  
      };  
      new GuiTextEditCtrl() {  
         canSaveDynamicFields = "0";  
         isContainer = "0";  
         Profile = "ToolsGuiTextEditProfile";  
         HorizSizing = "right";  
         VertSizing = "bottom";  
         Position = "97 124";  
         Extent = "66 18";  
         MinExtent = "8 2";  
         canSave = "1";  
         Visible = "1";  
         Variable = "$TPPSlopeMax";  
         tooltipprofile = "ToolsGuiToolTipProfile";  
         hovertime = "1000";  
         Margin = "0 0 0 0";  
         Padding = "0 0 0 0";  
         AnchorTop = "1";  
         AnchorBottom = "0";  
         AnchorLeft = "1";  
         AnchorRight = "0";  
         maxLength = "1024";  
         historySize = "0";  
         password = "0";  
         tabComplete = "0";  
         sinkAllKeyEvents = "0";  
         passwordMask = "*";  
      };  


      new GuiTextCtrl() {
         text = "COVERAGE";
         maxLength = "1024";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         position = "10 165";
         extent = "55 13";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         profile = "ToolsGuiTextProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "GuiToolTipProfile";
         hovertime = "1000";
         isContainer = "0";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
      new GuiTextEditCtrl() {
         historySize = "0";
         tabComplete = "0";
         sinkAllKeyEvents = "0";
         password = "0";
         passwordMask = "*";
         text = "1";
         maxLength = "1024";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         position = "97 162";
         extent = "66 18";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         profile = "ToolsGuiTextEditProfile";
         visible = "1";
         active = "1";
         variable = "$TPPCoverage";
         tooltipProfile = "GuiToolTipProfile";
         hovertime = "1000";
         isContainer = "0";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
      new GuiTextCtrl() {
         text = "%";
         maxLength = "1024";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         position = "77 164";
         extent = "11 14";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         profile = "ToolsGuiTextProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         isContainer = "0";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
   };  
};  
//--- OBJECT WRITE END ---  
  
$TPPHeightMin = -10000;  
$TPPHeightMax = 10000;  
$TPPSlopeMin = 0;  
$TPPSlopeMax = 90;  
$TPPCoverage = 100;  
  
function autoLayers() 
{  
   Canvas.pushDialog(ProceduralTerrainPainterGui);  
}  
  
function generateProceduralTerrainMask() 
{  
   Canvas.popDialog(ProceduralTerrainPainterGui);  
   ETerrainEditor.autoMaterialLayer($TPPHeightMin, $TPPHeightMax, $TPPSlopeMin, $TPPSlopeMax, $TPPCoverage);  
}  

