{
    "sample_car": {
        "information": {
            "name": "Sample Car",
            "authors": "BeamNG Replica Team"
        },
        
        // Basic chassis nodes - simple box structure
        "nodes": [
            // Front nodes
            ["n0", -1.0, 2.0, 0.0, {"nodeWeight": 25}],
            ["n1",  1.0, 2.0, 0.0, {"nodeWeight": 25}],
            ["n2", -1.0, 2.0, 1.0, {"nodeWeight": 25}],
            ["n3",  1.0, 2.0, 1.0, {"nodeWeight": 25}],
            
            // Rear nodes
            ["n4", -1.0, -2.0, 0.0, {"nodeWeight": 25}],
            ["n5",  1.0, -2.0, 0.0, {"nodeWeight": 25}],
            ["n6", -1.0, -2.0, 1.0, {"nodeWeight": 25}],
            ["n7",  1.0, -2.0, 1.0, {"nodeWeight": 25}],
            
            // Wheel nodes
            ["wheel_fl", -1.2, 1.8, -0.3, {"nodeWeight": 15}], // Front left wheel
            ["wheel_fr",  1.2, 1.8, -0.3, {"nodeWeight": 15}], // Front right wheel
            ["wheel_rl", -1.2, -1.8, -0.3, {"nodeWeight": 15}], // Rear left wheel
            ["wheel_rr",  1.2, -1.8, -0.3, {"nodeWeight": 15}]  // Rear right wheel
        ],
        
        // Structural beams connecting the nodes
        "beams": [
            // Bottom frame
            ["n0", "n1", {"beamSpring": 4000000, "beamDamp": 500}],
            ["n1", "n5", {"beamSpring": 4000000, "beamDamp": 500}],
            ["n5", "n4", {"beamSpring": 4000000, "beamDamp": 500}],
            ["n4", "n0", {"beamSpring": 4000000, "beamDamp": 500}],
            
            // Top frame
            ["n2", "n3", {"beamSpring": 4000000, "beamDamp": 500}],
            ["n3", "n7", {"beamSpring": 4000000, "beamDamp": 500}],
            ["n7", "n6", {"beamSpring": 4000000, "beamDamp": 500}],
            ["n6", "n2", {"beamSpring": 4000000, "beamDamp": 500}],
            
            // Vertical supports
            ["n0", "n2", {"beamSpring": 4000000, "beamDamp": 500}],
            ["n1", "n3", {"beamSpring": 4000000, "beamDamp": 500}],
            ["n4", "n6", {"beamSpring": 4000000, "beamDamp": 500}],
            ["n5", "n7", {"beamSpring": 4000000, "beamDamp": 500}],
            
            // Cross braces for rigidity
            ["n0", "n5", {"beamSpring": 3000000, "beamDamp": 400}],
            ["n1", "n4", {"beamSpring": 3000000, "beamDamp": 400}],
            ["n2", "n7", {"beamSpring": 3000000, "beamDamp": 400}],
            ["n3", "n6", {"beamSpring": 3000000, "beamDamp": 400}],
            
            // Diagonal braces
            ["n0", "n3", {"beamSpring": 2000000, "beamDamp": 300}],
            ["n1", "n2", {"beamSpring": 2000000, "beamDamp": 300}],
            ["n4", "n7", {"beamSpring": 2000000, "beamDamp": 300}],
            ["n5", "n6", {"beamSpring": 2000000, "beamDamp": 300}],
            
            // Wheel connections (suspension)
            ["n0", "wheel_fl", {"beamSpring": 500000, "beamDamp": 2000}],
            ["n2", "wheel_fl", {"beamSpring": 500000, "beamDamp": 2000}],
            ["n1", "wheel_fr", {"beamSpring": 500000, "beamDamp": 2000}],
            ["n3", "wheel_fr", {"beamSpring": 500000, "beamDamp": 2000}],
            ["n4", "wheel_rl", {"beamSpring": 500000, "beamDamp": 2000}],
            ["n6", "wheel_rl", {"beamSpring": 500000, "beamDamp": 2000}],
            ["n5", "wheel_rr", {"beamSpring": 500000, "beamDamp": 2000}],
            ["n7", "wheel_rr", {"beamSpring": 500000, "beamDamp": 2000}]
        ]
    }
}
