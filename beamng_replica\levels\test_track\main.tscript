//-----------------------------------------------------------------------------
// Test Track Level - BeamNG Replica
// Main level script for testing soft-body vehicles
//-----------------------------------------------------------------------------

// Level information
$levelName = "Test Track";
$levelDescription = "Basic test environment for soft-body vehicle physics";

//-----------------------------------------------------------------------------
// Level initialization
//-----------------------------------------------------------------------------
function initTestTrack() {
    echo("Initializing Test Track level...");
    
    // Load the mission file
    loadMission("beamng_replica/levels/test_track/test_track.mis");
    
    // Set up the environment
    setupEnvironment();
    
    // Create test vehicle
    createTestVehicle();
    
    // Set up camera
    setupCamera();
    
    // Set up controls
    setupControls();
    
    echo("Test Track level initialized successfully!");
}

function setupEnvironment() {
    // Configure lighting and atmosphere
    echo("Setting up environment...");
    
    // Set time of day
    $timeOfDay = 0.5; // Noon
    
    // Configure fog
    $fogEnabled = false;
    
    // Set gravity
    $gravityMagnitude = 9.81;
}

function createTestVehicle() {
    echo("Creating test vehicle...");
    
    // Get spawn position
    %spawnPoint = nameToID("VehicleSpawn");
    if (!isObject(%spawnPoint)) {
        error("Vehicle spawn point not found!");
        return;
    }
    
    %position = %spawnPoint.getTransform();
    
    // Create the soft-body vehicle
    $testVehicle = createSoftBodyVehicle(%position, "beamng_replica/jbeam/vehicles/sample_car.jbeam");
    
    if (isObject($testVehicle)) {
        echo("Test vehicle created successfully: " @ $testVehicle.getName());
        
        // Add to mission cleanup
        MissionCleanup.add($testVehicle);
    } else {
        error("Failed to create test vehicle!");
    }
}

function setupCamera() {
    echo("Setting up camera...");
    
    // Create observer camera
    new Observer(TestCamera) {
        dataBlock = "Observer";
    };
    
    // Get camera spawn position
    %cameraSpawn = nameToID("CameraSpawn");
    if (isObject(%cameraSpawn)) {
        %position = %cameraSpawn.getTransform();
        TestCamera.setTransform(%position);
    } else {
        // Default camera position
        TestCamera.setTransform("0 -10 8 1 0 0 0");
    }
    
    // Set as control object
    LocalClientConnection.setControlObject(TestCamera);
    
    // Add to mission cleanup
    MissionCleanup.add(TestCamera);
}

function setupControls() {
    echo("Setting up vehicle controls...");
    
    // Bind vehicle control keys
    GlobalActionMap.bind("keyboard", "w", "throttleForward");
    GlobalActionMap.bind("keyboard", "s", "throttleReverse");
    GlobalActionMap.bind("keyboard", "a", "steerLeft");
    GlobalActionMap.bind("keyboard", "d", "steerRight");
    GlobalActionMap.bind("keyboard", "space", "brake");
    
    // Camera controls
    GlobalActionMap.bind("keyboard", "c", "switchCamera");
    GlobalActionMap.bind("keyboard", "r", "resetVehicle");
    
    // Debug controls
    GlobalActionMap.bind("keyboard", "f1", "togglePhysicsDebug");
    GlobalActionMap.bind("keyboard", "f2", "toggleWireframe");
    
    echo("Controls bound successfully!");
    echo("Vehicle Controls:");
    echo("  W/S - Throttle Forward/Reverse");
    echo("  A/D - Steer Left/Right");
    echo("  Space - Brake");
    echo("  C - Switch Camera");
    echo("  R - Reset Vehicle");
    echo("  F1 - Toggle Physics Debug");
    echo("  F2 - Toggle Wireframe");
}

//-----------------------------------------------------------------------------
// Control functions
//-----------------------------------------------------------------------------
function throttleForward(%val) {
    if (isObject($testVehicle) && %val) {
        $testVehicle.updateControls(1.0, $currentSteering, 0.0);
    }
}

function throttleReverse(%val) {
    if (isObject($testVehicle) && %val) {
        $testVehicle.updateControls(-1.0, $currentSteering, 0.0);
    }
}

function steerLeft(%val) {
    $currentSteering = %val ? -1.0 : 0.0;
    if (isObject($testVehicle)) {
        $testVehicle.updateControls($currentThrottle, $currentSteering, $currentBrake);
    }
}

function steerRight(%val) {
    $currentSteering = %val ? 1.0 : 0.0;
    if (isObject($testVehicle)) {
        $testVehicle.updateControls($currentThrottle, $currentSteering, $currentBrake);
    }
}

function brake(%val) {
    $currentBrake = %val ? 1.0 : 0.0;
    if (isObject($testVehicle)) {
        $testVehicle.updateControls($currentThrottle, $currentSteering, $currentBrake);
    }
}

function switchCamera(%val) {
    if (!%val) return;
    
    if (isObject($testVehicle)) {
        if (LocalClientConnection.getControlObject() == TestCamera) {
            // Switch to vehicle camera
            LocalClientConnection.setControlObject($testVehicle);
            echo("Switched to vehicle camera");
        } else {
            // Switch to observer camera
            LocalClientConnection.setControlObject(TestCamera);
            echo("Switched to observer camera");
        }
    }
}

function resetVehicle(%val) {
    if (!%val) return;
    
    if (isObject($testVehicle)) {
        echo("Resetting vehicle...");
        $testVehicle.reset();
        
        // Reset position
        %spawnPoint = nameToID("VehicleSpawn");
        if (isObject(%spawnPoint)) {
            %position = %spawnPoint.getTransform();
            $testVehicle.setTransform(%position);
        }
    }
}

function togglePhysicsDebug(%val) {
    if (!%val) return;
    
    $physicsDebugEnabled = !$physicsDebugEnabled;
    echo("Physics debug: " @ ($physicsDebugEnabled ? "ON" : "OFF"));
    
    // This would enable/disable physics visualization
    if (isObject($testVehicle) && $testVehicle.softBodyPhysics) {
        $testVehicle.softBodyPhysics.setDebugRendering($physicsDebugEnabled);
    }
}

function toggleWireframe(%val) {
    if (!%val) return;
    
    $wireframeEnabled = !$wireframeEnabled;
    echo("Wireframe mode: " @ ($wireframeEnabled ? "ON" : "OFF"));
    
    // Toggle wireframe rendering
    $pref::Video::wireframe = $wireframeEnabled;
}

//-----------------------------------------------------------------------------
// Level cleanup
//-----------------------------------------------------------------------------
function cleanupTestTrack() {
    echo("Cleaning up Test Track level...");
    
    // Clear control bindings
    GlobalActionMap.unbind("keyboard", "w");
    GlobalActionMap.unbind("keyboard", "s");
    GlobalActionMap.unbind("keyboard", "a");
    GlobalActionMap.unbind("keyboard", "d");
    GlobalActionMap.unbind("keyboard", "space");
    GlobalActionMap.unbind("keyboard", "c");
    GlobalActionMap.unbind("keyboard", "r");
    GlobalActionMap.unbind("keyboard", "f1");
    GlobalActionMap.unbind("keyboard", "f2");
    
    // Reset global variables
    $currentThrottle = 0.0;
    $currentSteering = 0.0;
    $currentBrake = 0.0;
    $physicsDebugEnabled = false;
    $wireframeEnabled = false;
    
    echo("Test Track cleanup complete!");
}
