//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(ManageSFXParametersContainer,EditorGuiGroup) {
   isContainer = "1";
   Profile = "ToolsGuiModelessDialogProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";
   canSaveDynamicFields = "1";
   enabled = "1";
   isDecoy = "0";

   new GuiWindowCollapseCtrl(EManageSFXParameters) {
      CollapseGroup = "-1";
      CollapseGroupNum = "-1";
      resizeWidth = "1";
      resizeHeight = "1";
      canMove = "1";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "50 50";
      closeCommand = "EManageSFXParameters.setVisible( false );";
      EdgeSnap = "0";
      text = "Audio Parameters";
      Margin = "5 5 5 5";
      Padding = "5 5 5 5";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      HorizSizing = "windowRelative";
      VertSizing = "windowRelative";
      position = "49 68";
      Extent = "446 392";
      MinExtent = "120 130";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      internalName = "ManageSFXParametersWindow";
      canSaveDynamicFields = "0";

      new GuiControl() {
         isContainer = "1";
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "width";
         VertSizing = "bottom";
         position = "4 23";
         Extent = "484 20";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";

         new GuiTextCtrl() {
            text = "Name";
            maxLength = "1024";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            isContainer = "0";
            Profile = "ToolsGuiAutoSizeTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "1 2";
            Extent = "29 17";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            canSaveDynamicFields = "0";
         };
         new GuiTextEditCtrl() {
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
            passwordMask = "•";
            maxLength = "1024";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            isContainer = "0";
            Profile = "ToolsGuiTextEditProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            position = "36 2";
            Extent = "226 17";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            internalName = "AddSFXParameterName";
            canSaveDynamicFields = "0";
         };
         new GuiBitmapButtonCtrl() {
            bitmapAsset = "ToolsModule:new_n_image";
            autoFit = "0";
            text = "Create";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "left";
            VertSizing = "bottom";
            position = "266 2";
            Extent = "17 17";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "EManageSFXParameters.createNewParameter( EManageSFXParameters-->AddSFXParameterName.getText() );";
            Accelerator = "return";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Create New SFX Parameter";
            hovertime = "1000";
            canSaveDynamicFields = "0";
         };
         new GuiPopUpMenuCtrl() {
            maxPopupHeight = "200";
            sbUsesNAColor = "0";
            reverseTextList = "0";
            bitmapBounds = "16 16";
            maxLength = "1024";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            isContainer = "0";
            Profile = "ToolsGuiPopUpMenuProfile";
            HorizSizing = "left";
            VertSizing = "bottom";
            position = "325 1";
            Extent = "113 19";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "EManageSFXParameters.initList( $ThisControl.getText() );";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            internalName = "SFXParameterFilter";
            canSaveDynamicFields = "0";
         };
         new GuiTextCtrl() {
            text = "Filter";
            maxLength = "1024";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            isContainer = "0";
            Profile = "ToolsGuiAutoSizeTextProfile";
            HorizSizing = "left";
            VertSizing = "bottom";
            position = "296 2";
            Extent = "24 17";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            canSaveDynamicFields = "0";
         };
      };
      new GuiScrollCtrl() {
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "1";
         lockVertScroll = "0";
         constantThumbHeight = "0";
         childMargin = "2 2";
         mouseWheelScrollSpeed = "-1";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         isContainer = "1";
         Profile = "ToolsGuiScrollProfile";
         HorizSizing = "width";
         VertSizing = "height";
         position = "4 46";
         Extent = "438 344";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";

         new GuiStackControl() {
            StackingType = "Vertical";
            HorizStacking = "Left to Right";
            VertStacking = "Top to Bottom";
            Padding = "2";
            DynamicSize = "1";
            ChangeChildSizeToFit = "1";
            ChangeChildPosition = "1";
            isContainer = "1";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            position = "3 3";
            Extent = "419 10008";
            MinExtent = "16 16";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            internalName = "SFXParametersStack";
            canSaveDynamicFields = "0";
         };
      };
   };
};
//--- OBJECT WRITE END ---
