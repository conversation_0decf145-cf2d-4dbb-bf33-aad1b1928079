$paletteId = new GuiControl(RiverEditorPalette,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   
   new GuiBitmapButtonCtrl(ERiverEditorSelectModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RiverEditorSelectMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RiverEditorGui.prepSelectionMode();";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Select River (1)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:arrow_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };    
   new GuiBitmapButtonCtrl(ERiverEditorMoveModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RiverEditorMoveMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RiverEditorGui.setMode(\"RiverEditorMoveMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Move Point (2)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:move_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ERiverEditorRotateModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RiverEditorRotateMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RiverEditorGui.setMode(\"RiverEditorRotateMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Rotate Point (3)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:rotate_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ERiverEditorScaleModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RiverEditorScaleMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RiverEditorGui.setMode(\"RiverEditorScaleMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Scale Point (4)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:scale_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ERiverEditorAddModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RiverEditorAddRiverMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RiverEditorGui.setMode(\"RiverEditorAddRiverMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Create River (5)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:add_river_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ERiverEditorInsertModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RiverEditorInsertPointMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RiverEditorGui.setMode(\"RiverEditorInsertPointMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Insert Point (+)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:add_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   
   new GuiBitmapButtonCtrl(ERiverEditorRemoveModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RiverEditorRemovePointMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RiverEditorGui.setMode(\"RiverEditorRemovePointMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Remove Point (-)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:subtract_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
};
