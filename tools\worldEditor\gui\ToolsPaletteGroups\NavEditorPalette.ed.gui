$paletteId = new GuiControl(NavEditorPalette,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "GuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   
   new GuiBitmapButtonCtrl(ENavEditorSelectModeBtn) {
      canSaveDynamicFields = "1";
      class = ENavEditorPaletteButton;
      internalName = "NavEditorSelectMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "NavEditorGui.prepSelectionMode();";
      tooltipprofile = "GuiToolTipProfile";
      ToolTip = "View NavMesh (1).";
      DetailedDesc = "";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:visibility_toggle_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ENavEditorLinkModeBtn) {
      canSaveDynamicFields = "1";
      class = ENavEditorPaletteButton;
      internalName = "NavEditorLinkMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "NavEditorGui.setMode(\"LinkMode\");";
      tooltipprofile = "GuiToolTipProfile";
      ToolTip = "Create off-mesh links (2).";
      DetailedDesc = "Click to select/add. Shift-click to add multiple end points.";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:nav_link_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ENavEditorCoverModeBtn) {
      canSaveDynamicFields = "1";
      class = ENavEditorPaletteButton;
      internalName = "NavEditorCoverMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "NavEditorGui.setMode(\"CoverMode\");";
      tooltipprofile = "GuiToolTipProfile";
      ToolTip = "Edit cover (3).";
      DetailedDesc = "";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:nav_cover_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ENavEditorTileModeBtn) {
      canSaveDynamicFields = "1";
      class = ENavEditorPaletteButton;
      internalName = "NavEditorTileMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "NavEditorGui.setMode(\"TileMode\");";
      tooltipprofile = "GuiToolTipProfile";
      ToolTip = "View tiles (4).";
      DetailedDesc = "Click to select.";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:select_bounds_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ENavEditorTestModeBtn) {
      canSaveDynamicFields = "1";
      class = ENavEditorPaletteButton;
      internalName = "NavEditorTestMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "NavEditorGui.setMode(\"TestMode\");";
      tooltipprofile = "GuiToolTipProfile";
      ToolTip = "Test pathfinding (5).";
      DetailedDesc = "Click to select/move character, CTRL-click to spawn, SHIFT-click to deselect.";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:3rd_person_camera_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
};
