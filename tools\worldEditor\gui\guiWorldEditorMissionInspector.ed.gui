//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(WorldEditorMissionInspector,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   position = "0 0";
   Extent = "800 600";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";

   new GuiWindowCtrl(EWInspectorWindow) {
      canSaveDynamicFields = "0";
      internalName = "InspectorWindow";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      HorizSizing = "left";
      VertSizing = "bottom";
      position = "333 26";
      Extent = "304 448";
      MinExtent = "304 448";
      canSave = "1";
      Visible = "0";
      hovertime = "1000";
      text = "Mission Inspector";
      maxLength = "1024";
      resizeWidth = "1";
      resizeHeight = "1";
      canMove = "1";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "50 50";
      closeCommand = "EWInspectorFrame.parentGroup.setVisible(false);";

      new GuiFrameSetCtrl(EWInspectorFrame) {
         canSaveDynamicFields = "0";
         isContainer = "1";
         Profile = "ToolsGuiFrameSetProfile";
         HorizSizing = "width";
         VertSizing = "height";
         position = "8 32";
         Extent = "288 408";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         columns = "0";
         rows = "0 206";
         borderWidth = "0";
         borderColor = "84 12 136 1";
         borderEnable = "dynamic";
         borderMovable = "dynamic";
         autoBalance = "0";
         fudgeFactor = "0";

         new GuiControl(EWTreePane) {
            canSaveDynamicFields = "0";
            isContainer = "1";
            Profile = "EditorDefaultProfile";
            HorizSizing = "width";
            VertSizing = "height";
            position = "0 0";
            Extent = "288 202";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            hovertime = "1000";

            new GuiScrollCtrl() {
               canSaveDynamicFields = "0";
               isContainer = "1";
               Profile = "ToolsGuiScrollProfile";
               HorizSizing = "width";
               VertSizing = "height";
               position = "0 0";
               Extent = "288 202";
               MinExtent = "8 8";
               canSave = "1";
               Visible = "1";
               hovertime = "1000";
               willFirstRespond = "1";
               hScrollBar = "alwaysOff";
               vScrollBar = "dynamic";
               lockHorizScroll = "true";
               lockVertScroll = "false";
               constantThumbHeight = "0";
               childMargin = "0 0";

               new GuiTreeViewCtrl(EditorTree) {
                  canSaveDynamicFields = "0";
                  isContainer = "1";
                  Profile = "ToolsGuiTreeViewProfile";
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  position = "2 2";
                  Extent = "226 21";
                  MinExtent = "8 8";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";
                  tabSize = "16";
                  textOffset = "2";
                  fullRowSelect = "0";
                  itemHeight = "21";
                  destroyTreeOnSleep = "1";
                  MouseDragging = "1";
                  MultipleSelections = "1";
                  DeleteObjectAllowed = "1";
                  DragToItemAllowed = "1";
               };
            };
         };
         new GuiControl(EWCreatorInspectorPane) {
            canSaveDynamicFields = "0";
            isContainer = "1";
            Profile = "EditorDefaultProfile";
            HorizSizing = "width";
            VertSizing = "height";
            position = "0 206";
            Extent = "288 202";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            hovertime = "1000";

            new GuiScrollCtrl(EWCreatorPane) {
               canSaveDynamicFields = "0";
               isContainer = "1";
               Profile = "ToolsGuiScrollProfile";
               HorizSizing = "width";
               VertSizing = "height";
               position = "0 0";
               Extent = "288 202";
               MinExtent = "8 8";
               canSave = "1";
               Visible = "0";
               hovertime = "1000";
               willFirstRespond = "1";
               hScrollBar = "dynamic";
               vScrollBar = "dynamic";
               lockHorizScroll = true;
               lockVertScroll = "false";
               constantThumbHeight = "0";
               childMargin = "0 0";

               new GuiTreeViewCtrl(Creator) {
                  canSaveDynamicFields = "0";
                  isContainer = "1";
                  Profile = "ToolsGuiTreeViewProfile";
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  position = "2 2";
                  Extent = "131 84";
                  MinExtent = "8 8";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";
                  tabSize = "16";
                  textOffset = "2";
                  fullRowSelect = "0";
                  itemHeight = "21";
                  destroyTreeOnSleep = "1";
                  MouseDragging = "1";
                  MultipleSelections = "1";
                  DeleteObjectAllowed = "1";
                  DragToItemAllowed = "1";
               };
            };
            new GuiControl(EWInspectorPane) {
               canSaveDynamicFields = "0";
               isContainer = "1";
               Profile = "EditorDefaultProfile";
               HorizSizing = "width";
               VertSizing = "height";
               position = "0 0";
               Extent = "288 202";
               MinExtent = "8 8";
               canSave = "1";
               Visible = "1";
               hovertime = "1000";

               new GuiControl() {
                  canSaveDynamicFields = "0";
                  isContainer = "1";
                  Profile = "ToolsGuiSolidDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "0 0";
                  Extent = "288 24";
                  MinExtent = "8 8";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";

                  new GuiButtonCtrl() {
                     canSaveDynamicFields = "0";
                     isContainer = "0";
                     Profile = "ToolsGuiButtonProfile";
                     HorizSizing = "right";
                     VertSizing = "bottom";
                     position = "2 2";
                     Extent = "40 20";
                     MinExtent = "8 8";
                     canSave = "1";
                     Visible = "1";
                     Command = "EWorldEditor.isDirty = true;EWorldEditor.makeFirstResponder(true);";
                     tooltipprofile = "ToolsGuiToolTipProfile";
                     ToolTip = "Don\'t forget to hit Apply after making changes!";
                     hovertime = "1000";
                     text = "Apply";
                     groupNum = "-1";
                     buttonType = "PushButton";
                     useMouseEvents = "0";
                  };
                  new GuiTextCtrl() {
                     canSaveDynamicFields = "0";
                     isContainer = "0";
                     Profile = "GuiInspectorFieldProfile";
                     HorizSizing = "right";
                     VertSizing = "bottom";
                     position = "52 4";
                     Extent = "42 16";
                     MinExtent = "8 2";
                     canSave = "1";
                     Visible = "1";
                     hovertime = "1000";
                     text = "Name:";
                     maxLength = "1024";
                  };
                  new GuiTextEditCtrl(InspectorNameEdit) {
                     canSaveDynamicFields = "0";
                     isContainer = "0";
                     Profile = "GuiInspectorBackgroundProfile";
                     HorizSizing = "width";
                     VertSizing = "bottom";
                     position = "97 4";
                     Extent = "758 18";
                     MinExtent = "8 2";
                     canSave = "1";
                     Visible = "1";
                     AltCommand = "EWorldEditor.isDirty = true;";
                     hovertime = "1000";
                     maxLength = "1024";
                     historySize = "0";
                     password = "0";
                     tabComplete = "0";
                     sinkAllKeyEvents = "0";
                     password = "0";
                     passwordMask = "*";
                  };
               };
               new GuiScrollCtrl() {
                  canSaveDynamicFields = "0";
                  isContainer = "1";
                  Profile = "ToolsGuiScrollProfile";
                  HorizSizing = "width";
                  VertSizing = "height";
                  position = "0 24";
                  Extent = "288 178";
                  MinExtent = "8 8";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";
                  willFirstRespond = "1";
                  hScrollBar = "dynamic";
                  vScrollBar = "dynamic";
                  lockHorizScroll = true;
                  lockVertScroll = "false";
                  constantThumbHeight = "0";
                  childMargin = "0 0";

                  new GuiInspector(Inspector) {
                     StackingType = "Vertical";
                     HorizStacking = "Left to Right";
                     VertStacking = "Top to Bottom";
                     Padding = "1";
                     canSaveDynamicFields = "0";
                     isContainer = "1";
                     Profile = "GuiInspectorBackgroundProfile";
                     HorizSizing = "width";
                     VertSizing = "bottom";
                     position = "2 2";
                     Extent = "266 8";
                     MinExtent = "8 8";
                     canSave = "1";
                     Visible = "1";
                     hovertime = "1000";
                  };
               };
            };
         };
      };
   };
};
//--- OBJECT WRITE END ---
