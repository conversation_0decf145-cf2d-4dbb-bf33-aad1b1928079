$paletteId = new GuiControl(TerrainEditorPalette,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   
   new GuiBitmapButtonCtrl() {
      canSaveDynamicFields = "0";
      internalName = "brushAdjustHeight";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = "72 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "ETerrainEditor.switchAction( brushAdjustHeight );";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Grab Terrain (1)";
      hovertime = "750";
      text = "Button";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      bitmapAsset = "ToolsModule:brushAdjustHeight_n_image";
   };
   new GuiBitmapButtonCtrl() {
      canSaveDynamicFields = "0";
      internalName = "raiseHeight";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "ETerrainEditor.switchAction( raiseHeight );";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Raise Height (2)";
      hovertime = "750";
      text = "Button";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      bitmapAsset = "ToolsModule:raiseHeight_n_image";
   };
   new GuiBitmapButtonCtrl() {
      canSaveDynamicFields = "0";
      internalName = "lowerHeight";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = "36 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "ETerrainEditor.switchAction( lowerHeight );";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Lower Height (3)";
      hovertime = "750";
      text = "Button";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      bitmapAsset = "ToolsModule:lowerHeight_n_image";
   };
   new GuiBitmapButtonCtrl() {
      canSaveDynamicFields = "0";
      internalName = "smoothHeight";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = "144 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "ETerrainEditor.switchAction( smoothHeight );";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Smooth (4)";
      hovertime = "750";
      text = "Button";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      bitmapAsset = "ToolsModule:smoothHeight_n_image";
   };
   new GuiBitmapButtonCtrl() {  
      canSaveDynamicFields = "0";  
      internalName = "smoothSlope";  
      Enabled = "1";  
      isContainer = "0";  
      Profile = "ToolsGuiButtonProfile";  
      HorizSizing = "right";  
      VertSizing = "bottom";  
      position = "144 0";  
      Extent = "25 19";  
      MinExtent = "8 2";  
      canSave = "1";  
      Visible = "1";  
      Command = "ETerrainEditor.switchAction( smoothSlope );";  
      tooltipprofile = "GuiToolTipProfile";  
      ToolTip = "Smooth Slope (5)";  
      hovertime = "750";  
      text = "Button";  
      buttonType = "RadioButton";  
      useMouseEvents = "0";  
      bitmapAsset = "ToolsModule:softCurve_n_image";  
   };  
   new GuiBitmapButtonCtrl() {
      canSaveDynamicFields = "0";
      internalName = "paintNoise";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = "72 36";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "ETerrainEditor.switchAction( paintNoise );";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Paint Noise (6)";
      hovertime = "750";
      text = "Button";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      bitmapAsset = "ToolsModule:brushPaintNoise_n_image";
   };   
   new GuiBitmapButtonCtrl() {
      canSaveDynamicFields = "0";
      internalName = "flattenHeight";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = "108 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "ETerrainEditor.switchAction( flattenHeight );";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Flatten (7)";
      hovertime = "750";
      text = "Button";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      bitmapAsset = "ToolsModule:flattenHeight_n_image";
   };
   new GuiBitmapButtonCtrl() {
      canSaveDynamicFields = "0";
      internalName = "setHeight";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = "180 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "ETerrainEditor.switchAction( setHeight );";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Set Height (8)";
      hovertime = "750";
      text = "Button";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      bitmapAsset = "ToolsModule:setHeight_n_image";
   };
   new GuiBitmapButtonCtrl() {
      canSaveDynamicFields = "0";
      internalName = "setEmpty";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = "0 36";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "ETerrainEditor.switchAction( setEmpty );";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Clear Terrain (9)";
      hovertime = "750";
      text = "Button";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      bitmapAsset = "ToolsModule:setEmpty_n_image";
   };
   new GuiBitmapButtonCtrl() {
      canSaveDynamicFields = "0";
      internalName = "clearEmpty";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = "36 36";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "ETerrainEditor.switchAction( clearEmpty );";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Restore Terrain (0)";
      hovertime = "750";
      text = "Button";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      bitmapAsset = "ToolsModule:clearEmpty_n_image";
   };
};
