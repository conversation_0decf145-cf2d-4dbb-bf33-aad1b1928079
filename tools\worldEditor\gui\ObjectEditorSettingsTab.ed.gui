//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(ObjectEditorSettingsTab,EditorGuiGroup) {
   isContainer = "1";
   profile = "ToolsGuiDefaultProfile";
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 2";
   visible = "1";
   active = "1";
   tooltipProfile = "ToolsGuiToolTipProfile";
   hovertime = "1000";
   canSave = "1";
   canSaveDynamicFields = "1";

   new GuiTabPageCtrl(EObjectEditorSettingsPage) {
      fitBook = "1";
      text = "Object Editor";
      maxLength = "1024";
      margin = "0 0 0 0";
      padding = "0 0 0 0";
      anchorTop = "1";
      anchorBottom = "0";
      anchorLeft = "1";
      anchorRight = "0";
      isContainer = "1";
      profile = "ToolsGuiSolidDefaultProfile";
      horizSizing = "width";
      vertSizing = "height";
      position = "0 0";
      extent = "208 568";
      minExtent = "8 2";
      visible = "1";
      active = "1";
      tooltipProfile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      canSave = "1";
      canSaveDynamicFields = "1";

      new GuiScrollCtrl() {
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "1";
         lockVertScroll = "0";
         constantThumbHeight = "0";
         childMargin = "0 0";
         mouseWheelScrollSpeed = "-1";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         isContainer = "1";
         profile = "ToolsGuiScrollProfile";
         horizSizing = "width";
         vertSizing = "height";
         position = "0 0";
         extent = "208 568";
         minExtent = "8 2";
         visible = "1";
         active = "1";
         tooltipProfile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSave = "1";
         canSaveDynamicFields = "0";

         new GuiStackControl() {
            StackingType = "Vertical";
            HorizStacking = "Left to Right";
            VertStacking = "Top to Bottom";
            Padding = "0";
            isContainer = "1";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            position = "1 1";
            extent = "208 210";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";

            new GuiRolloutCtrl() {
               Profile = "GuiRolloutProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               position = "10 10";
               extent = "208 95";
               Caption = "Render";
               Margin = "4 3 0 0";
               DragSizable = false;
               container = true;
                
               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  isContainer = "1";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "0 0";
                  Extent = "208 0";
                  MinExtent = "8 2";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000"; 
                  padding = "3";

                  new GuiCheckBoxCtrl() {
                     useInactiveState = "0";
                     text = "Object Icons";
                     groupNum = "-1";
                     buttonType = "ToggleButton";
                     useMouseEvents = "0";
                     isContainer = "0";
                     profile = "ToolsGuiCheckBoxProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 10";
                     extent = "140 14";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowCheckbox";
                        editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                        editorSettingsValue = "WorldEditor/Render/renderObjHandle";
                        editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                  };
                  new GuiCheckBoxCtrl() {
                     useInactiveState = "0";
                     text = "Object Text";
                     groupNum = "-1";
                     buttonType = "ToggleButton";
                     useMouseEvents = "0";
                     isContainer = "0";
                     profile = "ToolsGuiCheckBoxProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 30";
                     extent = "140 14";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowCheckbox";
                        editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                        editorSettingsValue = "WorldEditor/Render/renderObjText";
                        editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                  };
                  new GuiCheckBoxCtrl() {
                     useInactiveState = "0";
                     text = "Mouse Popup Info";
                     groupNum = "-1";
                     buttonType = "ToggleButton";
                     useMouseEvents = "0";
                     isContainer = "0";
                     profile = "ToolsGuiCheckBoxProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 50";
                     extent = "140 14";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowCheckbox";
                        editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                        editorSettingsValue = "WorldEditor/Render/showMousePopupInfo";
                        editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                  };
                  new GuiCheckBoxCtrl() {
                     useInactiveState = "0";
                     text = "Popup Menu Background";
                     groupNum = "-1";
                     buttonType = "ToggleButton";
                     useMouseEvents = "0";
                     isContainer = "0";
                     profile = "ToolsGuiCheckBoxProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 70";
                     extent = "140 14";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowCheckbox";
                        editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                        editorSettingsValue = "WorldEditor/Render/renderPopupBackground";
                        editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                  };
               };
            };
            new GuiRolloutCtrl() {
               Profile = "GuiRolloutProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               position = "10 10";
               extent = "208 95";
               Caption = "Colors";
               Margin = "0 3 0 0";
               DragSizable = false;
               container = true;
                
               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  isContainer = "1";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "0 0";
                  Extent = "208 0";
                  MinExtent = "8 2";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000"; 
                  padding = "3";

                  new GuiControl() {
                     isContainer = "1";
                     profile = "ToolsGuiDefaultProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 90";
                     extent = "204 18";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowColor";
                        editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                        editorSettingsValue = "WorldEditor/Grid/gridColor";
                        editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";

                     new GuiTextCtrl() {
                        text = "Grid Major:";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextRightProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "0 1";
                        extent = "70 16";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextEditProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "80 0";
                        extent = "104 18";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorEdit";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorEdit";
                     };
                     new GuiSwatchButtonCtrl() {
                        color = "1 1 1 1";
                        groupNum = "-1";
                        buttonType = "PushButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiDefaultProfile";
                        horizSizing = "left";
                        vertSizing = "bottom";
                        position = "188 2";
                        extent = "14 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorButton";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorButton";
                     };
                  };
                  new GuiControl() {
                     isContainer = "1";
                     profile = "ToolsGuiDefaultProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 110";
                     extent = "204 18";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowColor";
                        editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                        editorSettingsValue = "WorldEditor/Grid/gridMinorColor";
                        editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";

                     new GuiTextCtrl() {
                        text = "Grid Minor:";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextRightProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "0 1";
                        extent = "70 16";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextEditProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "80 0";
                        extent = "104 18";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorEdit";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorEdit";
                     };
                     new GuiSwatchButtonCtrl() {
                        color = "1 1 1 1";
                        groupNum = "-1";
                        buttonType = "PushButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiDefaultProfile";
                        horizSizing = "left";
                        vertSizing = "bottom";
                        position = "188 2";
                        extent = "14 14";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorButton";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorButton";
                     };
                  };
                  new GuiControl() {
                     isContainer = "1";
                     profile = "ToolsGuiDefaultProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 130";
                     extent = "204 18";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowColor";
                        editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                        editorSettingsValue = "WorldEditor/Grid/gridOriginColor";
                        editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";

                     new GuiTextCtrl() {
                        text = "Grid Origin:";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextRightProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "0 1";
                        extent = "70 16";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextEditProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "80 0";
                        extent = "104 17";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorEdit";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorEdit";
                     };
                     new GuiSwatchButtonCtrl() {
                        color = "1 1 1 1";
                        groupNum = "-1";
                        buttonType = "PushButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiDefaultProfile";
                        horizSizing = "left";
                        vertSizing = "bottom";
                        position = "188 2";
                        extent = "14 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorButton";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorButton";
                     };
                  };
                  new GuiControl() {
                     isContainer = "1";
                     profile = "ToolsGuiDefaultProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 10";
                     extent = "204 18";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowColor";
                        editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                        editorSettingsValue = "WorldEditor/Color/dragRectColor";
                        editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";

                     new GuiTextCtrl() {
                        text = "Drag Rect:";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextRightProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "0 1";
                        extent = "70 16";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextEditProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "80 0";
                        extent = "104 18";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorEdit";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorEdit";
                     };
                     new GuiSwatchButtonCtrl() {
                        color = "1 1 1 1";
                        groupNum = "-1";
                        buttonType = "PushButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiDefaultProfile";
                        horizSizing = "left";
                        vertSizing = "bottom";
                        position = "188 2";
                        extent = "14 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorButton";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorButton";
                     };
                  };
                  new GuiControl() {
                     isContainer = "1";
                     profile = "ToolsGuiDefaultProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 30";
                     extent = "204 18";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowColor";
                        editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                        editorSettingsValue = "WorldEditor/Color/objectTextColor";
                        editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";

                     new GuiTextCtrl() {
                        text = "Object Text:";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextRightProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "0 1";
                        extent = "70 16";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextEditProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "80 0";
                        extent = "104 18";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorEdit";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorEdit";
                     };
                     new GuiSwatchButtonCtrl() {
                        color = "1 1 1 1";
                        groupNum = "-1";
                        buttonType = "PushButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiDefaultProfile";
                        horizSizing = "left";
                        vertSizing = "bottom";
                        position = "188 2";
                        extent = "14 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorButton";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorButton";
                     };
                  };
                  new GuiControl() {
                     isContainer = "1";
                     profile = "ToolsGuiDefaultProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 50";
                     extent = "204 18";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowColor";
                        editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                        editorSettingsValue = "WorldEditor/Color/popupTextColor";
                        editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";

                     new GuiTextCtrl() {
                        text = "Popup Text:";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextRightProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "0 1";
                        extent = "70 16";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextEditProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "80 0";
                        extent = "104 18";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorEdit";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorEdit";
                     };
                     new GuiSwatchButtonCtrl() {
                        color = "1 1 1 1";
                        groupNum = "-1";
                        buttonType = "PushButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiDefaultProfile";
                        horizSizing = "left";
                        vertSizing = "bottom";
                        position = "188 2";
                        extent = "14 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorButton";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorButton";
                     };
                  };
                  new GuiControl() {
                     isContainer = "1";
                     profile = "ToolsGuiDefaultProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 70";
                     extent = "204 18";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowColor";
                        editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                        editorSettingsValue = "WorldEditor/Color/popupBackgroundColor";
                        editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";

                     new GuiTextCtrl() {
                        text = "Popup Back:";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextRightProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "0 1";
                        extent = "70 16";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextEditProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "80 0";
                        extent = "104 18";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorEdit";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorEdit";
                     };
                     new GuiSwatchButtonCtrl() {
                        color = "1 1 1 1";
                        groupNum = "-1";
                        buttonType = "PushButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiDefaultProfile";
                        horizSizing = "left";
                        vertSizing = "bottom";
                        position = "188 2";
                        extent = "14 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        internalName = "ColorButton";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowColorButton";
                     };
                  };
               };
            };
            new GuiRolloutCtrl() {
               Profile = "GuiRolloutProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               position = "10 10";
               extent = "208 95";
               Caption = "Misc";
               Margin = "0 3 0 0";
               DragSizable = false;
               container = true;
                
               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  Padding = "0";
                  isContainer = "1";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "0 0";
                  Extent = "208 0";
                  MinExtent = "8 2";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000"; 
                  padding = "3";
                  
                  new GuiControl() {
                     isContainer = "1";
                     profile = "ToolsGuiDefaultProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "0 0";
                     extent = "210 14";
                     
                     new GuiCheckBoxCtrl() {
                        useInactiveState = "0";
                        text = "Force Load DAE";
                        groupNum = "-1";
                        buttonType = "ToggleButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiCheckBoxProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "5 0";
                        extent = "140 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        variable = "EWorldEditor.forceLoadDAE";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowCheckbox";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "WorldEditor/forceLoadDAE";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                  };
                  new GuiControl() {
                     isContainer = "1";
                     profile = "ToolsGuiDefaultProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 70";
                     extent = "210 18";

                     new GuiTextCtrl() {
                        text = "Screen Center Scalar:";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "5 1";
                        extent = "110 16";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "0";
                     };
                     
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiNumericTextEditProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "120 0";
                        extent = "80 18";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowTextEdit";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "WorldEditor/Tools/dropAtScreenCenterScalar";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                  };
                  new GuiControl() {
                     isContainer = "1";
                     profile = "ToolsGuiDefaultProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 70";
                     extent = "210 18";
                     
                     new GuiTextCtrl() {
                        text = "Screen Center Max:";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiTextProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "5 1";
                        extent = "110 16";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "0";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        isContainer = "0";
                        profile = "ToolsGuiNumericTextEditProfile";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        position = "120 0";
                        extent = "80 18";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowTextEdit";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "WorldEditor/Tools/dropAtScreenCenterMax";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                  };
               };
            };
         };
      };
   };
};
//--- OBJECT WRITE END ---
