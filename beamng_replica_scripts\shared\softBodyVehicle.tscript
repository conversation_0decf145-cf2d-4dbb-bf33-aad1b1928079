//-----------------------------------------------------------------------------
// Soft-Body Vehicle System for BeamNG Replica
// TorqueScript integration for JBEAM-based vehicles
//-----------------------------------------------------------------------------

// Vehicle class definition
datablock ShapeBaseData(SoftBodyVehicleData) {
    category = "Vehicles";
    shapeFile = "beamng_replica/assets/vehicles/sample_car.dae";
    
    // Physics properties
    mass = 1000;
    drag = 0.3;
    density = 1.0;
    
    // Damage and destruction
    destroyedLevel = 0.8;
    destructionTimeout = 5000;
    
    // Engine properties
    maxDamage = 100;
    damageBuffer = 20;
    
    // Collision properties
    boundingBox = "2.5 5.0 2.0";
    crouchBoundingBox = "2.5 5.0 2.0";
    
    // Rendering
    renderWhenDestroyed = false;
    explosion = "";
    
    // Custom properties for soft-body physics
    jbeamFile = "beamng_replica/jbeam/vehicles/sample_car.jbeam";
    enableSoftBodyPhysics = true;
    physicsUpdateRate = 120; // Hz
};

//-----------------------------------------------------------------------------
// SoftBodyVehicle class - main vehicle object
//-----------------------------------------------------------------------------
function SoftBodyVehicleData::create(%data, %obj) {
    // Initialize the soft-body physics system
    if (%data.enableSoftBodyPhysics) {
        %obj.initializeSoftBodyPhysics(%data.jbeamFile);
    }
    
    // Set up vehicle controls
    %obj.setupVehicleControls();
    
    // Initialize rendering system
    %obj.initializeRendering();
    
    return %obj;
}

function SoftBodyVehicleData::onAdd(%data, %obj) {
    // Called when vehicle is added to the scene
    echo("SoftBodyVehicle added: " @ %obj.getName());
    
    // Start physics simulation
    if (%obj.softBodyPhysics) {
        %obj.startPhysicsSimulation();
    }
}

function SoftBodyVehicleData::onRemove(%data, %obj) {
    // Clean up physics simulation
    if (%obj.softBodyPhysics) {
        %obj.stopPhysicsSimulation();
    }
    
    echo("SoftBodyVehicle removed: " @ %obj.getName());
}

//-----------------------------------------------------------------------------
// Vehicle control methods
//-----------------------------------------------------------------------------
function SoftBodyVehicle::setupVehicleControls(%this) {
    // Initialize control variables
    %this.throttleInput = 0.0;
    %this.steeringInput = 0.0;
    %this.brakeInput = 0.0;
    
    // Control sensitivity
    %this.steeringSensitivity = 1.0;
    %this.throttleSensitivity = 1.0;
    %this.brakeSensitivity = 1.0;
    
    // Vehicle state
    %this.engineRunning = true;
    %this.speed = 0.0;
}

function SoftBodyVehicle::updateControls(%this, %throttle, %steering, %brake) {
    // Update control inputs
    %this.throttleInput = mClampF(%throttle, -1.0, 1.0);
    %this.steeringInput = mClampF(%steering, -1.0, 1.0);
    %this.brakeInput = mClampF(%brake, 0.0, 1.0);
    
    // Apply forces to physics system
    if (%this.softBodyPhysics) {
        %this.applyDrivingForces();
    }
}

function SoftBodyVehicle::applyDrivingForces(%this) {
    // Calculate engine force
    %engineForce = %this.throttleInput * 5000.0; // Newtons
    
    // Apply force to rear wheels
    if (%this.softBodyPhysics) {
        // Apply forward/backward force to rear wheel nodes
        %forwardVector = %this.getForwardVector();
        %force = VectorScale(%forwardVector, %engineForce);
        
        // Apply to rear wheels
        %this.softBodyPhysics.applyForce("wheel_rl", %force);
        %this.softBodyPhysics.applyForce("wheel_rr", %force);
        
        // Apply steering forces to front wheels
        if (%this.steeringInput != 0) {
            %steerForce = %this.steeringInput * 2000.0;
            %rightVector = %this.getRightVector();
            %steerForceVector = VectorScale(%rightVector, %steerForce);
            
            %this.softBodyPhysics.applyForce("wheel_fl", %steerForceVector);
            %this.softBodyPhysics.applyForce("wheel_fr", %steerForceVector);
        }
        
        // Apply brake forces
        if (%this.brakeInput > 0) {
            %brakeForce = %this.brakeInput * -3000.0;
            %brakeVector = VectorScale(%forwardVector, %brakeForce);
            
            // Apply to all wheels
            %this.softBodyPhysics.applyForce("wheel_fl", %brakeVector);
            %this.softBodyPhysics.applyForce("wheel_fr", %brakeVector);
            %this.softBodyPhysics.applyForce("wheel_rl", %brakeVector);
            %this.softBodyPhysics.applyForce("wheel_rr", %brakeVector);
        }
    }
}

//-----------------------------------------------------------------------------
// Physics integration methods
//-----------------------------------------------------------------------------
function SoftBodyVehicle::initializeSoftBodyPhysics(%this, %jbeamFile) {
    // This would interface with our C++ SoftBodyPhysics class
    echo("Initializing soft-body physics from: " @ %jbeamFile);
    
    // Create physics system (C++ integration point)
    %this.softBodyPhysics = new SoftBodyPhysics();
    
    // Load JBEAM file
    if (!%this.softBodyPhysics.loadJBeamFile(%jbeamFile)) {
        error("Failed to load JBEAM file: " @ %jbeamFile);
        return false;
    }
    
    // Enable gravity
    %this.softBodyPhysics.setGravityEnabled(true);
    %this.softBodyPhysics.setGravity("0 0 -9.81");
    
    return true;
}

function SoftBodyVehicle::startPhysicsSimulation(%this) {
    // Start the physics update loop
    %this.physicsSchedule = %this.schedule(8, "updatePhysics"); // ~120 Hz
}

function SoftBodyVehicle::stopPhysicsSimulation(%this) {
    // Stop the physics update loop
    if (%this.physicsSchedule) {
        cancel(%this.physicsSchedule);
        %this.physicsSchedule = "";
    }
}

function SoftBodyVehicle::updatePhysics(%this) {
    if (%this.softBodyPhysics) {
        // Update physics simulation
        %deltaTime = 1.0 / 120.0; // Fixed timestep
        %this.softBodyPhysics.update(%deltaTime);
        
        // Update vehicle position based on physics
        %this.updateVehicleTransform();
        
        // Schedule next update
        %this.physicsSchedule = %this.schedule(8, "updatePhysics");
    }
}

function SoftBodyVehicle::updateVehicleTransform(%this) {
    // Calculate vehicle center of mass from physics nodes
    if (%this.softBodyPhysics) {
        %centerOfMass = %this.softBodyPhysics.getCenterOfMass();
        %this.setTransform(%centerOfMass @ " 0 0 1 0");
    }
}

//-----------------------------------------------------------------------------
// Utility functions
//-----------------------------------------------------------------------------
function createSoftBodyVehicle(%position, %jbeamFile) {
    %vehicle = new SoftBodyVehicle() {
        dataBlock = SoftBodyVehicleData;
        position = %position;
    };
    
    if (%jbeamFile !$= "") {
        %vehicle.getDataBlock().jbeamFile = %jbeamFile;
    }
    
    return %vehicle;
}

function SoftBodyVehicle::reset(%this) {
    // Reset physics simulation to initial state
    if (%this.softBodyPhysics) {
        %this.softBodyPhysics.reset();
    }
    
    // Reset control inputs
    %this.throttleInput = 0.0;
    %this.steeringInput = 0.0;
    %this.brakeInput = 0.0;
}
