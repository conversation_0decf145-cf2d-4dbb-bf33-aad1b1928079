//*****************************************************************************
// Torque -- HLSL procedural shader
//*****************************************************************************

// Dependencies:
#include "core/rendering/shaders/torque.hlsl"

// Features:
// Vert Position
// Diffuse Color
// Deferred Shading: PBR Config Explicit Numbers
// Deferred Shading: Mat Info Flags
// Visibility
// Debug Viz
// HDR Output

struct ConnectData
{
   float4 vpos            : SV_Position;
};


struct Fragout
{
   float4 col : SV_Target0;
};


//-----------------------------------------------------------------------------
// Main
//-----------------------------------------------------------------------------
Fragout main( ConnectData IN,
              uniform float4    diffuseMaterialColor : register(C0),
              uniform float     metalness       : register(C1),
              uniform float     roughness       : register(C2),
              uniform float     matInfoFlags    : register(C3),
              uniform float     visibility      : register(C4)
)
{
   Fragout OUT;

   // Vert Position
   
   // Diffuse Color
   OUT.col = diffuseMaterialColor;
   
   // Deferred Shading: PBR Config Explicit Numbers
   float4 ORMConfig;
   ORMConfig.g = 1.0;
   ORMConfig.b = roughness;
   ORMConfig.a = metalness;
   
   // Deferred Shading: Mat Info Flags
   ORMConfig.r = matInfoFlags;
   
   // Visibility
   fizzle( IN.vpos.xy, visibility );
   
   // Debug Viz
   
   // HDR Output
   OUT.col = hdrEncode( OUT.col );
   

   return OUT;
}
