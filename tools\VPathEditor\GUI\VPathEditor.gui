//-----------------------------------------------------------------------------
// Verve
// Copyright (C) - Violent Tulip
//-----------------------------------------------------------------------------

new VPathEditor(EVPathEditor) {
    canSaveDynamicFields = "0";
    Enabled = "1";
    isContainer = "0";
    Profile = "VPathEditorProfile";
    HorizSizing = "width";
    VertSizing = "height";
    Position = "0 0";
    Extent = "800 600";
    MinExtent = "8 8";
    canSave = "1";
    Visible = "1";
    tooltipprofile = "GuiToolTipProfile";
    hovertime = "1000";
    Docking = "None";
    Margin = "0 0 0 0";
    Padding = "0 0 0 0";
    AnchorTop = "0";
    AnchorBottom = "0";
    AnchorLeft = "0";
    AnchorRight = "0";
    cameraZRot = "0";
    forceFOV = "0";
    renderMissionArea = "1";
    missionAreaFillColor = "255 0 0 20";
    missionAreaFrameColor = "255 0 0 128";
    allowBorderMove = "0";
    borderMovePixelSize = "20";
    borderMoveSpeed = "0.1";
    consoleFrameColor = "255 0 0 255";
    consoleFillColor = "0 0 0 0";
    consoleSphereLevel = "1";
    consoleCircleSegments = "32";
    consoleLineWidth = "1";
    GizmoProfile = "GlobalGizmoProfile";

   new GuiWindowCollapseCtrl(VPathEditorTreeWindow) {
      internalName = "";
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "GuiWindowProfile";
      HorizSizing = "windowRelative";
      VertSizing = "windowRelative";
      Position = getWord($pref::Video::mode, 0) - 209
         SPC getWord(EditorGuiToolbar.extent, 1) - 1;
      Extent = "210 167";
      MinExtent = "210 100";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "GuiToolTipProfile";
      hovertime = "1000";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      resizeWidth = "1";
      resizeHeight = "1";
      canMove = "1";
      canClose = "0";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "50 50";
      closeCommand = "EditorGui.setEditor( WorldEditorInspectorPlugin );";
      EdgeSnap = "1";
      text = "Path Editor";
      
      new GuiContainer(){
         profile = GuiDefaultProfile;
         Position = "5 25";
         Extent = "200 120";
         Docking = "Client";
         Margin = "3 1 3 3 ";
         HorizSizing = "width";
         VertSizing = "height";
         isContainer = "1";
         
         new GuiScrollCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "1";
            Profile = "GuiEditorScrollProfile";
            HorizSizing = "width";
            VertSizing = "height";
            Position = "0 0";
            Extent = "200 118";
            MinExtent = "8 8";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "GuiToolTipProfile";
            hovertime = "1000";
            Docking = "Client";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            willFirstRespond = "1";
            hScrollBar = "alwaysOff";
            vScrollBar = "dynamic";
            lockHorizScroll = "true";
            lockVertScroll = "false";
            constantThumbHeight = "0";
            childMargin = "0 0";
            mouseWheelScrollSpeed = "-1";

            new GuiTreeViewCtrl(VPathTreeView) {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "1";
               Profile = "ToolsGuiTreeViewProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               Position = "1 1";
               Extent = "193 21";
               MinExtent = "8 8";
               canSave = "1";
               Visible = "1";
               hovertime = "1000";
               tabSize = "16";
               textOffset = "2";
               fullRowSelect = "0";
               itemHeight = "21";
               destroyTreeOnSleep = "1";
               MouseDragging = "0";
               MultipleSelections = "0";
               DeleteObjectAllowed = "1";
               DragToItemAllowed = "0";
               showRoot = "1";
               internalNamesOnly = "0";
            };
         };
      };
      new GuiBitmapButtonCtrl() {
         bitmapAsset = "ToolsModule:delete_n_image";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
         isContainer = "0";
         Profile = "GuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         position = "193 4";
         Extent = "16 16";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "VPathTreeView.DeleteSelectedPaths();";
         tooltipprofile = "GuiToolTipProfile";
         ToolTip = "Delete Selected Path";
         hovertime = "1000";
         internalName = "deleteSelection";
         canSaveDynamicFields = "0";
      };
      new GuiBitmapButtonCtrl() {
         bitmap = "core/gui/images/new";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
         isContainer = "0";
         Profile = "GuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         position = "176 3";
         Extent = "17 17";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "VPathTreeView.CreatePath();";
         tooltipprofile = "GuiToolTipProfile";
         tooltip = "Create New Path";
         hovertime = "1000";
         internalName = "CreateSelection";
         canSaveDynamicFields = "0";
      };
   };
   new GuiWindowCollapseCtrl(VPathEditorOptionsWindow) {
      internalName = "Window";
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "GuiWindowProfile";
      HorizSizing = "windowRelative";
      VertSizing = "windowRelative";
      Position = getWord($pref::Video::mode, 0) - 209
         SPC getWord(EditorGuiToolbar.extent, 1) + getWord(VPathEditorTreeWindow.extent, 1) - 2;
      Extent = "210 530";
      MinExtent = "210 298";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "GuiToolTipProfile";
      hovertime = "1000";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      resizeWidth = "1";
      resizeHeight = "1";
      canMove = "1";
      canClose = "0";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "50 50";
      closeCommand = "EditorGui.setEditor( WorldEditorPlugin );";
      EdgeSnap = "1";
      text = "Properties";

      new GuiContainer(){ //Node Properties
         isContainer = "1";
         Profile = "inspectorStyleRolloutDarkProfile";
         HorizSizing = "width";
         VertSizing = "bottom";
         Position = "4 24";
         Extent = "202 127";
         Docking = "Top";
         Margin = "3 3 3 3";
         
         new GuiTextCtrl(){
            Profile = "GuiDefaultProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 0";
            Extent = "86 18";
            text = "Node Properties";
         };
         new GuiTextCtrl(){
            Profile = "GuiTextRightProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "7 21";
            Extent = "46 18";
            text = "Position";
         };
         new GuiTextEditCtrl(){
            internalName = "position";
            Profile = "GuiTextEditProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            Position = "57 21";
            Extent = "141 18";
            text = "";
            AltCommand = "EVPathEditor.setNodePosition( $ThisControl.getValue() );";
         };
         new GuiTextCtrl(){
            Profile = "GuiTextRightProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "7 42";
            Extent = "46 18";
            text = "Rotation";
         };
         new GuiTextEditCtrl(){
            internalName = "rotation";
            Profile = "GuiTextEditProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            Position = "57 42";
            Extent = "141 18";
            text = "";
            AltCommand = "EVPathEditor.setNodeRotation( $ThisControl.getValue() );";
         };
         new GuiTextCtrl(){
            Profile = "GuiTextRightProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "7 63";
            Extent = "46 18";
            text = "Weight";
         };
         new GuiTextEditCtrl(){
            internalName = "weight";
            Profile = "GuiTextEditProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "57 63";
            Extent = "52 18";
            text = "";
            AltCommand = "EVPathEditor.setNodeWeight( $ThisControl.getValue() );";
         };
         new GuiTextCtrl(){
            Profile = "GuiTextRightProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "7 84";
            Extent = "46 18";
            text = "Orientation";
         };
         new GuiPopUpMenuCtrl(EPathEditorNodeOrientationMode){
            internalName = "weight";
            Profile = "GuiPopUpMenuProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "57 84";
            Extent = "141 18";
            text = "";
            Command = "OnOrientationChanged();";
         };
         new GuiTextCtrl(){
            Profile = "GuiTextRightProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "7 105";
            Extent = "46 18";
            text = "Lookat Pt";
         };
         new GuiTextEditCtrl(EPathEditorNodeOrientationData){
            internalName = "weight";
            Profile = "GuiTextEditProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "57 105";
            Extent = "141 18";
            text = "";
            AltCommand = "OnOrientationChanged();";
         };
      };
      new GuiContainer(){ // Path Properties
         isContainer = "1";
         Profile = "inspectorStyleRolloutDarkProfile";
         HorizSizing = "width";
         VertSizing = "bottom";
         Position = "4 112";
         Extent = "202 31";
         Docking = "Top";
         Margin = "0 0 3 3";
         
         new GuiTextCtrl(){
            Profile = "GuiDefaultProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "5 0";
            Extent = "121 18";
            text = "Path Properties";
         };
      };
      new GuiScrollCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "GuiEditorScrollProfile";
         HorizSizing = "width";
         VertSizing = "height";
         Position = "4 150";
         Extent = "223 315";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "GuiToolTipProfile";
         hovertime = "1000";
         Docking = "Client";
         Margin = "-14 41 3 3";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "true";
         lockVertScroll = "false";
         constantThumbHeight = "0";
         childMargin = "0 0";

         new GuiInspector(VPathInspector) {
            StackingType = "Vertical";
            HorizStacking = "Left to Right";
            VertStacking = "Top to Bottom";
            Padding = "1";
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "1";
            Profile = "GuiTransparentProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "1 1";
            Extent = "179 16";
            MinExtent = "16 16";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "GuiToolTipProfile";
            hovertime = "1000";
            dividerMargin = "5";
         };
      };   
      new GuiMLTextCtrl(VPathFieldInfoControl) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "GuiInspectorFieldInfoMLTextProfile";
         HorizSizing = "width";
         VertSizing = "top";
         Position = "1 485";
         Extent = "202 42";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "GuiToolTipProfile";
         hovertime = "1000";
         lineSpacing = "2";
         allowColorChars = "0";
         maxChars = "-1";
         useURLMouseCursor = "0";
      };
   };
};
//--- OBJECT WRITE END ---

function EPathEditorNodeOrientationMode::onWake( %this )
{
    if ( %this.size() == 0 )
    {
        %this.add( "FREE",    0 );
        %this.add( "TOPOINT", 1 );
    }
}

function OnOrientationChanged()
{
    %mode = EPathEditorNodeOrientationMode.getText();
    %data = EPathEditorNodeOrientationData.getText();
    EVPathEditor.setNodeOrientationMode( %mode, %data );
}
