//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(TerrainExportGui, EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Profile = "ToolsGuiOverlayProfile";
   Enabled = "1";
   isContainer = "1";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";

   new GuiWindowCtrl(TerrainExportWindow) {
      profile = "ToolsGuiWindowProfile";
      canSaveDynamicFields = "0";
      internalName = "TerrainExport";
      Enabled = "1";
      isContainer = "1";
      HorizSizing = "center";
      VertSizing = "center";
      Position = "248 248";
      Extent = "290 235";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      hovertime = "1000";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "1";
      AnchorLeft = "1";
      AnchorRight = "1";
      resizeWidth = "0";
      resizeHeight = "0";
      canMove = "1";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "50 50";
      closeCommand = "TerrainExportGui.close();";
      EdgeSnap = "0";
      canCollapse = "0";
      text = "Export Terrain";

      new GuiScrollCtrl(TerrainSelectScroll) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "9 43";
         Extent = "272 112";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "true";
         lockVertScroll = "false";
         constantThumbHeight = "0";
         childMargin = "0 0";

         new GuiListBoxCtrl(TerrainSelectListBox) {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "2 2";
            Extent = "248 104";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            hovertime = "1000";
            AllowMultipleSelections = "1";
            fitParentWidth = "1";
         };
      };
      new GuiTextCtrl(TerrainSelectText) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "9 25";
         Extent = "88 16";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "Select Terrain(s):";
         maxLength = "1024";
      };
      new GuiTextEditCtrl(SelectFolderTextEdit) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "9 176";
         Extent = "195 18";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         maxLength = "1024";
         historySize = "0";
         password = "0";
         tabComplete = "0";
         sinkAllKeyEvents = "0";
         passwordMask = "*";
      };
      new GuiButtonCtrl(SelectFolderButton) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "212 174";
         Extent = "69 22";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "TerrainExportGui.selectFolder();";
         hovertime = "1000";
         text = "Browse";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiButtonCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "174 202";
         Extent = "107 24";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "TerrainExportGui.close();";
         hovertime = "1000";
         text = "Cancel";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiButtonCtrl(ExportButton) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "9 202";
         Extent = "107 24";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "TerrainExportGui.export();";
         hovertime = "1000";
         text = "Export";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiTextCtrl(FolderText) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "9 159";
         Extent = "96 16";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "Folder:";
         maxLength = "1024";
      };
   };
};
//--- OBJECT WRITE END ---

function TerrainExportGui::findAllTerrains( %this )
{
   TerrainSelectListBox.clearItems();

   if ( isObject( MegaTerrain ) )
      TerrainSelectListBox.addItem( "MegaTerrain" );

   // Find all of the terrain files
   initContainerTypeSearch( $TypeMasks::TerrainObjectType );

   while ( (%terrainObject = containerSearchNext()) != 0 )
   {
      %terrainId = %terrainObject.getId();
      %terrainName = %terrainObject.getName();
      if ( %terrainName $= "" )
         %terrainName = "Unnamed (" @ %terrainId @ ")";

      TerrainSelectListBox.addItem( %terrainName, %terrainId );
   }
}

function TerrainExportGui::init( %this )
{
   %this.findAllTerrains();
}

function TerrainExportGui::export( %this )
{
   %itemId = TerrainSelectListBox.getSelectedItem();
   %terrainObj = TerrainSelectListBox.getItemObject( %itemId );
   if ( !isObject( %terrainObj ) )
   {
      toolsMessageBoxOK( "Export failed", "Could not find the selected TerrainBlock!" );
      return;
   }

   %filePath = SelectFolderTextEdit.getText();

   %terrainName = %terrainObj.getName();
   if ( %terrainName $= "" )
      %terrainName = "Unnamed";

   %fileName = %terrainName @ "_heightmap.png";
   %filePrefix = %terrainName @ "_layerMap";

   %ret = %terrainObj.exportHeightMap( %filePath @ "/" @ %fileName, "png" );
   if ( %ret )
      %ret = %terrainObj.exportLayerMaps( %filePath @ "/" @ %filePrefix, "png" );

   if ( %ret )
      %this.close();
}

function TerrainExportGui::onWake( %this )
{
   TerrainExportGui.init();
}

function TerrainExportGui::close( %this )
{
   Canvas.popDialog( %this );
}

function TerrainExportGui::showExportDialog( %this )
{
   %this.findAllTerrains();

   Canvas.pushDialog( %this );
}

function TerrainExportGui::openFolderCallback( %this, %path )
{
   SelectFolderTextEdit.setText( %path );
}

function TerrainExportGui::selectFolder( %this )
{
   %this.doOpenDialog( "", %this @ ".openFolderCallback" );
}

function TerrainExportGui::doOpenDialog( %this, %filter, %callback )
{
   // TODO
   %currentFile = "";

   %dlg = new OpenFolderDialog()
   {
      Title = "Select Export Folder";
      Filters = %filter;
      DefaultFile = %currentFile;
      ChangePath = false;
      MustExist = true;
      MultipleFiles = false;
   };

   if(filePath( %currentFile ) !$= "")
      %dlg.DefaultPath = filePath(%currentFile);
   else
      %dlg.DefaultPath = getMainDotCSDir();

   if(%dlg.Execute())
      eval(%callback @ "(\"" @ %dlg.FileName @ "\");");

   %dlg.delete();
}
