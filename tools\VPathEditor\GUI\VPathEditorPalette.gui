//-----------------------------------------------------------------------------
// Verve
// Copyright (C) - Violent Tulip
//-----------------------------------------------------------------------------

$paletteId = new GuiControl(VPathEditorPalette) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "GuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   
   new GuiBitmapButtonCtrl(EVPathEditorSelectButton) {
      canSaveDynamicFields = "0";
      internalName = "";
      Enabled = "1";
      isContainer = "0";
      Profile = "GuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "GuiToolTipProfile";
      ToolTip = "Select Path / Node (1)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:arrow_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(EVPathEditorMoveButton) {
      canSaveDynamicFields = "0";
      internalName = "";
      Enabled = "1";
      isContainer = "0";
      Profile = "GuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "GuiToolTipProfile";
      ToolTip = "Move Point (2)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:move_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(EVPathEditorRotateButton) {
      canSaveDynamicFields = "0";
      internalName = "";
      Enabled = "1";
      isContainer = "0";
      Profile = "GuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "28 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "GuiToolTipProfile";
      ToolTip = "Rotate Point (3)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:rotate_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(EVPathEditorScaleButton) {
      canSaveDynamicFields = "0";
      internalName = "";
      Enabled = "1";
      isContainer = "0";
      Profile = "GuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "56 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "GuiToolTipProfile";
      ToolTip = "Scale Point (4)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:scale_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(EVPathEditorAddNodeButton) {
      canSaveDynamicFields = "0";
      internalName = "";
      Enabled = "1";
      isContainer = "0";
      Profile = "GuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "28 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "GuiToolTipProfile";
      ToolTip = "Add Node (5)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:add_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(EVPathEditorDeleteNodeButton) {
      canSaveDynamicFields = "0";
      internalName = "";
      Enabled = "1";
      isContainer = "0";
      Profile = "GuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "56 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "GuiToolTipProfile";
      ToolTip = "Delete Node (6)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:subtract_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
};

//-----------------------------------------------------------------------------

function EVPathEditorSelectButton::onClick( %this )
{
    EVPathEditor.EditMode   = "Gizmo";
    GlobalGizmoProfile.Mode = "Select";
}

function EVPathEditorMoveButton::onClick( %this )
{
    EVPathEditor.EditMode   = "Gizmo";
    GlobalGizmoProfile.Mode = "Move";
}

function EVPathEditorRotateButton::onClick( %this )
{
    EVPathEditor.EditMode   = "Gizmo";
    GlobalGizmoProfile.Mode = "Rotate";
}

function EVPathEditorScaleButton::onClick( %this )
{
    EVPathEditor.EditMode   = "Gizmo";
    GlobalGizmoProfile.Mode = "Scale";
}

function EVPathEditorAddNodeButton::onClick( %this )
{
    EVPathEditor.EditMode = "AddNode";
}

function EVPathEditorDeleteNodeButton::onClick( %this )
{
    EVPathEditor.EditMode = "DeleteNode";
}
