//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(GeneralSettingsTab,EditorGuiGroup) {
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 2";
   horizSizing = "right";
   vertSizing = "bottom";
   profile = "ToolsGuiDefaultProfile";
   visible = "1";
   active = "1";
   tooltipProfile = "ToolsGuiToolTipProfile";
   hovertime = "1000";
   isContainer = "1";
   canSave = "1";
   canSaveDynamicFields = "1";

   new GuiTabPageCtrl(EGeneralSettingsPage) {
      fitBook = "1";
      text = "General Settings";
      maxLength = "1024";
      margin = "0 0 0 0";
      padding = "0 0 0 0";
      anchorTop = "1";
      anchorBottom = "0";
      anchorLeft = "1";
      anchorRight = "0";
      position = "0 0";
      extent = "432 568";
      minExtent = "8 2";
      horizSizing = "width";
      vertSizing = "height";
      profile = "ToolsGuiSolidDefaultProfile";
      visible = "1";
      active = "1";
      tooltipProfile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      isContainer = "1";
      canSave = "1";
      canSaveDynamicFields = "1";

      new GuiScrollCtrl() {
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "1";
         lockVertScroll = "0";
         constantThumbHeight = "0";
         childMargin = "0 0";
         mouseWheelScrollSpeed = "-1";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         position = "0 0";
         extent = "432 568";
         minExtent = "8 2";
         horizSizing = "width";
         vertSizing = "height";
         profile = "ToolsGuiScrollProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         isContainer = "1";
         canSave = "1";
         canSaveDynamicFields = "0";

         new GuiStackControl() {
            stackingType = "Vertical";
            horizStacking = "Left to Right";
            vertStacking = "Top to Bottom";
            padding = "0";
            dynamicSize = "1";
            changeChildSizeToFit = "1";
            changeChildPosition = "1";
            position = "1 1";
            extent = "430 41";
            minExtent = "8 2";
            horizSizing = "width";
            vertSizing = "bottom";
            profile = "ToolsGuiDefaultProfile";
            visible = "1";
            active = "1";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            isContainer = "1";
            canSave = "1";
            canSaveDynamicFields = "0";

            new GuiRolloutCtrl() {
               caption = "Paths";
               margin = "0 3 0 0";
               defaultHeight = "40";
               expanded = "1";
               clickCollapse = "1";
               hideHeader = "0";
               autoCollapseSiblings = "0";
               position = "0 0";
               extent = "430 41";
               minExtent = "8 2";
               horizSizing = "right";
               vertSizing = "bottom";
               profile = "GuiRolloutProfile";
               visible = "1";
               active = "1";
               tooltipProfile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               isContainer = "1";
               canSave = "1";
               canSaveDynamicFields = "0";

               new GuiStackControl() {
                  stackingType = "Vertical";
                  horizStacking = "Left to Right";
                  vertStacking = "Top to Bottom";
                  padding = "3";
                  dynamicSize = "1";
                  changeChildSizeToFit = "1";
                  changeChildPosition = "1";
                  position = "0 20";
                  extent = "430 18";
                  minExtent = "8 2";
                  horizSizing = "width";
                  vertSizing = "bottom";
                  profile = "ToolsGuiDefaultProfile";
                  visible = "1";
                  active = "1";
                  tooltipProfile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
                  isContainer = "1";
                  canSave = "1";
                  canSaveDynamicFields = "0";

                  new GuiControl() {
                     position = "0 0";
                     extent = "430 18";
                     minExtent = "8 2";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     profile = "ToolsGuiDefaultProfile";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     isContainer = "1";
                     canSave = "1";
                     canSaveDynamicFields = "0";

                     new GuiTextCtrl() {
                        text = "New Level";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        position = "5 1";
                        extent = "70 16";
                        minExtent = "8 2";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        profile = "ToolsGuiTextRightProfile";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        isContainer = "0";
                        canSave = "1";
                        canSaveDynamicFields = "0";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        password = "0";
                        passwordMask = "*";
                        text = "tools/levels/BlankRoom.mis";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        position = "81 0";
                        extent = "345 17";
                        minExtent = "8 2";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        profile = "ToolsGuiTextEditProfile";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        isContainer = "0";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowTextEdit";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                  };
                  new GuiControl() {
                     position = "0 0";
                     extent = "430 18";
                     minExtent = "8 2";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     profile = "ToolsGuiDefaultProfile";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     isContainer = "1";
                     canSave = "1";
                     canSaveDynamicFields = "0";

                     new GuiTextCtrl() {
                        text = "New Game Objects";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        position = "5 1";
                        extent = "70 16";
                        minExtent = "8 2";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        profile = "ToolsGuiTextRightProfile";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        isContainer = "0";
                        canSave = "1";
                        canSaveDynamicFields = "0";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        password = "0";
                        passwordMask = "*";
                        text = "scripts/server/gameObjects";
                        maxLength = "1024";
                        margin = "0 0 0 0";
                        padding = "0 0 0 0";
                        anchorTop = "1";
                        anchorBottom = "0";
                        anchorLeft = "1";
                        anchorRight = "0";
                        position = "81 0";
                        extent = "345 17";
                        minExtent = "8 2";
                        horizSizing = "width";
                        vertSizing = "bottom";
                        profile = "ToolsGuiTextEditProfile";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        isContainer = "0";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowTextEdit";
                        editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                        editorSettingsValue = "WorldEditor/newGameObjectDir";
                        editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                  };
               };
            };
         };
      };
   };
};
//--- OBJECT WRITE END ---
