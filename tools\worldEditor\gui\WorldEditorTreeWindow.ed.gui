//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl() {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "800 600";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";

   new GuiWindowCollapseCtrl(EWTreeWindow) {
      canSaveDynamicFields = "0";
      internalName = "TreeWindow";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      Position = firstWord($pref::Video::mode) - 330
         SPC getWord(EditorGuiToolbar.extent, 1) + 40;
      Extent = "300 270";
      MinExtent = "200 250";
      HorizSizing = "windowRelative";
      VertSizing = "windowRelative";
      canSave = "1";
      Visible = "1";
      hovertime = "1000";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      resizeWidth = "1";
      resizeHeight = "1";
      canMove = "1";
      canClose = "0";
      canMinimize = "1";
      canMaximize = "0";
      minSize = "50 50";
      closeCommand = "EWTreeWindow.setVisible(false);";
      EdgeSnap = "1";
      text = "Scene Tree";
      
      new GuiTabBookCtrl(EditorTreeTabBook) {
         canSaveDynamicFields = "0";
         isContainer = "1";
         internalName = "EditorTree";
         Profile = "ToolsGuiTabBookProfile";
         HorizSizing = "width";
         VertSizing = "height";
         position = "6 27";
         Extent = "197 289";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Docking = "client";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         TabPosition = "Top";
         TabMargin = "0";
         MinTabWidth = "65";
         
         new GuiTabPageCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "1";
            Profile = "ToolsGuiEditorTabPage";
            HorizSizing = "width";
            VertSizing = "height";
            position = "0 19";
            Extent = "197 271";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "Scene";
            maxLength = "1024";

            new GuiTextEditCtrl( EditorTreeFilter ) {
               position = "5 6";
               extent = "187 25";
               profile = "ToolsGuiTextEditProfile";
               horizSizing = "width";
               vertSizing = "bottom";
               class = "GuiTreeViewFilterText";
               treeView = EditorTree;
            };
            
            new GuiBitmapButtonCtrl() {
               bitmapAsset = "ToolsModule:clear_icon_n_image";
               groupNum = "-1";
               buttonType = "PushButton";
               useMouseEvents = "0";
               isContainer = "0";
               Profile = "ToolsGuiDefaultProfile";
               HorizSizing = "left";
               VertSizing = "bottom";
               position = "171 6";
               Extent = "17 17";
               MinExtent = "8 2";
               canSave = "1";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               canSaveDynamicFields = "0";
               class = "GuiTreeViewFilterClearButton";
               textCtrl = EditorTreeFilter;
            };
                              
            new GuiScrollCtrl() {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "1";
               Profile = "ToolsGuiScrollProfile";
               HorizSizing = "width";
               VertSizing = "height";
               Position = "5 29";
               Extent = "187 238";
               MinExtent = "8 8";
               canSave = "1";
               Visible = "1";
               hovertime = "1000";
               willFirstRespond = "1";
               hScrollBar = "dynamic";
               vScrollBar = "alwaysOn";
               lockHorizScroll = "false";
               lockVertScroll = "false";
               constantThumbHeight = "0";
               childMargin = "0 0";

               new GuiTreeViewCtrl(EditorTree) {
                  canSaveDynamicFields = "0";
                  Enabled = "1";
                  isContainer = "1";
                  Profile = "ToolsGuiTreeViewProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  Position = "0 0";
                  Extent = "197 25";
                  MinExtent = "8 8";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";
                  tabSize = "16";
                  textOffset = "5";
                  fullRowSelect = "1";
                  itemHeight = "25";
                  destroyTreeOnSleep = "1";
                  MouseDragging = "1";
                  MultipleSelections = "1";
                  DeleteObjectAllowed = "1";
                  DragToItemAllowed = "1";
                  showRoot = "1";
                  useInspectorTooltips = "1";
                  tooltipOnWidthOnly = "1";
                  showObjectIds = "0";
                  showClassNames = "0";
                  showObjectNames = "1";
                  showInternalNames = "1";
                  showClassNameForUnnamedObjects = "1";
               };
            };
         };
      
         new GuiTabPageCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "1";
            Profile = "ToolsGuiEditorTabPage";
            HorizSizing = "width";
            VertSizing = "height";
            position = "0 19";
            Extent = "197 271";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "Assets";
            maxLength = "1024";
         };
      };
      new GuiBitmapButtonCtrl() {
         canSaveDynamicFields = "0";
         internalName = "LockSelection";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         Position = "225 23";
         Extent = "16 16";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "EWorldEditor.lockSelection(true); EditorTree.toggleLock();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Lock Selection";
         hovertime = "1000";
         bitmapAsset = "ToolsModule:lock_n_image";
         buttonType = "ToggleButton";
         groupNum = "-1";
         text = "";
         useMouseEvents = "0";
      };
      
      new GuiBitmapButtonCtrl(EWAddSimGroupButton) {
         canSaveDynamicFields = "0";
         internalName = "AddSimGroup";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         Position = "250 23";
         Extent = "16 16";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Add Sim Group";
         hovertime = "1000";
         bitmapAsset = "ToolsModule:add_simgroup_btn_n_image";
         buttonType = "PushButton";
         groupNum = "-1";
         text = "";
         useMouseEvents = "0";
         useModifiers = "1";
      };
      
      new GuiBitmapButtonCtrl() {
         canSaveDynamicFields = "0";
         internalName = "DeleteSelection";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         Position = "275 23";
         Extent = "16 16";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         Command = "EditorMenuEditDelete();";
         tooltipprofile = "ToolsGuiToolTipProfile";
         ToolTip = "Delete Selection";
         hovertime = "1000";
         bitmapAsset = "ToolsModule:delete_n_image";
         buttonType = "PushButton";
         groupNum = "-1";
         text = "";
         useMouseEvents = "0";
      };
   };
};
//--- OBJECT WRITE END ---
