//*****************************************************************************
// Torque -- HLSL procedural shader
//*****************************************************************************

// Dependencies:
#include "core/rendering/shaders/torque.hlsl"

// Features:
// Vert Position
// Base Texture
// Deferred Shading: PBR Config Explicit Numbers
// Deferred Shading: Mat Info Flags
// Visibility
// Debug Viz
// HDR Output

struct ConnectData
{
   float4 vpos            : SV_Position;
   float2 texCoord        : TEXCOORD0;
};


struct Fragout
{
   float4 col : SV_Target0;
};


//-----------------------------------------------------------------------------
// Main
//-----------------------------------------------------------------------------
Fragout main( ConnectData IN,
              uniform SamplerState diffuseMap      : register(S0),
              uniform Texture2D diffuseMapTex   : register(T0),
              uniform float     metalness       : register(C0),
              uniform float     roughness       : register(C1),
              uniform float     matInfoFlags    : register(C2),
              uniform float     visibility      : register(C3)
)
{
   Fragout OUT;

   // Vert Position
   
   // Base Texture
float4 diffuseColor = diffuseMapTex.Sample(diffuseMap, IN.texCoord);
   OUT.col = diffuseColor;
   
   // Deferred Shading: PBR Config Explicit Numbers
   float4 ORMConfig;
   ORMConfig.g = 1.0;
   ORMConfig.b = roughness;
   ORMConfig.a = metalness;
   
   // Deferred Shading: Mat Info Flags
   ORMConfig.r = matInfoFlags;
   
   // Visibility
   fizzle( IN.vpos.xy, visibility );
   
   // Debug Viz
   
   // HDR Output
   OUT.col = hdrEncode( OUT.col );
   

   return OUT;
}
