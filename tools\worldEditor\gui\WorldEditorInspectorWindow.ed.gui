//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl() {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "800 600";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";

   new GuiWindowCollapseCtrl(EWInspectorWindow) {
      canSaveDynamicFields = "0";
      internalName = "InspectorWindow";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      Position = getWord($pref::Video::mode, 0) - 330 SPC 
      getWord(EditorGuiToolbar.extent, 1) + getWord(EWTreeWindow.extent, 1) + 40;
      Extent = "300 250";
      MinExtent = "200 150";
      HorizSizing = "windowRelative";
      VertSizing = "windowRelative";
      canSave = "1";
      Visible = "1";
      hovertime = "1000";
      Margin = "5 5 5 5";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      resizeWidth = "1";
      resizeHeight = "1";
      canMove = "1";
      canClose = "0";
      canMinimize = "0";
      canMaximize = "0";
      closeCommand = "EWInspectorWindow.setVisible(false);";
      minSize = "50 50";
      EdgeSnap = "1";
      text = "Inspector";

      new GuiContainer() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "width";
         VertSizing = "height";
         Position = "5 24";
         Extent = "202 304";
         MinExtent = "64 64";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Docking = "client";
         Margin = "0 43 0 5";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";

         new GuiScrollCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "1";
            Profile = "GuiEditorScrollProfile";
            HorizSizing = "width";
            VertSizing = "height";
            Position = "5 5";
            Extent = "187 238";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            Docking = "Client";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            willFirstRespond = "1";
            hScrollBar = "alwaysOff";
            vScrollBar = "alwaysOn";
            lockHorizScroll = "true";
            lockVertScroll = "false";
            constantThumbHeight = "0";
            childMargin = "0 0";

            new GuiInspector(Inspector) {
               StackingType = "Vertical";
               HorizStacking = "Left to Right";
               VertStacking = "Top to Bottom";
               Padding = "1";
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "1";
               Profile = "GuiInspectorProfile";
               HorizSizing = "width";
               VertSizing = "bottom";
               Position = "0 0";
               Extent = "202 309";
               MinExtent = "8 8";
               canSave = "1";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               dividerMargin = "5";
               superClass = "EditorInspectorBase";
            };
         };
      };
      new GuiMLTextCtrl(FieldInfoControl) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "GuiInspectorFieldInfoMLTextProfile";
         HorizSizing = "width";
         VertSizing = "top";
         Position = 5 SPC EWInspectorWindow.extent.y - 40;
         Extent = "197 35";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         lineSpacing = "3";
         allowColorChars = "0";
         maxChars = "-1";
         useURLMouseCursor = "0";
      };
   };
};
//--- OBJECT WRITE END ---
