$paletteId = new GuiControl(DecalEditorPalette,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   
   new GuiBitmapButtonCtrl(EDecalEditorSelectDecalBtn) {
      canSaveDynamicFields = "0";
      internalName = "SelectDecalMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "DecalEditorGui.setMode(\"SelectDecalMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Select Decal (1)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:arrow_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   
   new GuiBitmapButtonCtrl(EDecalEditorMoveDecalBtn) {
      canSaveDynamicFields = "0";
      internalName = "MoveDecalMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "DecalEditorGui.setMode(\"MoveDecalMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Move Decal (2)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:move_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(EDecalEditorRotateDecalBtn) {
      canSaveDynamicFields = "0";
      internalName = "RotateDecalMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "DecalEditorGui.setMode(\"RotateDecalMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Rotate Decal (3)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:rotate_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(EDecalEditorScaleDecalBtn) {
      canSaveDynamicFields = "0";
      internalName = "ScaleDecalMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "DecalEditorGui.setMode(\"ScaleDecalMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Scale Decal (4)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:scale_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
      new GuiBitmapButtonCtrl(EDecalEditorAddDecalBtn) {
      canSaveDynamicFields = "0";
      internalName = "AddDecalMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "DecalEditorGui.setMode(\"AddDecalMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Add Decal (5)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:add_decal_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
};
