//-----------------------------------------------------------------------------
// Verve
// Copyright (C) - Violent Tulip
//-----------------------------------------------------------------------------

function VerveEditor::CreateToggleEnumField( %fieldContainer, %fieldName, %class )
{
    // Create Enum Menu.
    %fieldInput = VerveEditor::CreateEnumField( %fieldContainer, %fieldName );
    
    // Populate Menu.
    %fieldInput.add( "On",  0 );
    %fieldInput.add( "Off", 1 );
    
    return %fieldInput;
}