//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(TransformSelectionContainer, EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiModelessDialogProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "800 600";
   MinExtent = "8 2";
   canSave = "1";
   isDecoy = "0";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";

   new GuiWindowCollapseCtrl(ETransformSelection) {
      internalName = "TransformSelectionWindow";
      Enabled = "1";
      isContainer = "1";
      profile = "ToolsGuiWindowProfile";
      HorizSizing = "windowRelative";
      VertSizing = "windowRelative";
      resizeWidth = "1";
      resizeHeight = "1";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      position = "40 70";
      extent = "180 508";
      MinExtent = "120 130";
      text = "Transform Selection";
      closeCommand = "ETransformSelection.hideDialog();";
      EdgeSnap = "0";
      canCollapse = "0";
      visible = "0";
      Margin = "5 5 5 5";
      Padding = "5 5 5 5";
            
      new GuiScrollCtrl() {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "ToolsGuiScrollProfile";
         HorizSizing = "width";
         VertSizing = "height";
         Position = "4 12";
         Extent = "156 190";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Docking = "Client";
         Margin = "5 5 5 4";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "true";
         lockVertScroll = "false";
         constantThumbHeight = "0";
         childMargin = "2 2";

         new GuiStackControl() {
            StackingType = "Vertical";
            HorizStacking = "Left to Right";
            VertStacking = "Top to Bottom";
            Padding = "5";
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "1";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            Position = "0 0";
            Extent = "140 300";
            MinExtent = "16 16";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            
            new GuiControl() {
               Enabled = "1";
               Profile = "ToolsGuiDefaultProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               Position = "0 0";
               Extent = "140 106";
               MinExtent = "16 16";
               Visible = "1";
               
               new GuiCheckBoxCtrl(){
                  class = "ETransformSelectionCheckBoxClass";
                  internalName = "DoPosition";
                  Enabled = "1";
                  Profile = "ToolsGuiCheckBoxProfile";
                  position = "1 0";
                  Extent = "190 18";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
                  tooltip = "Apply changes to position";
                  text = "Position";
                  Command = "";
               };
               
               new GuiButtonCtrl() {
                  class = "ETransformSelectionButtonClass";
                  internalName = "GetPosButton";
                  canSaveDynamicFields = "0";
                  Enabled = "1";
                  isContainer = "0";
                  Profile = "ToolsGuiButtonProfile";
                  HorizSizing = "left";
                  VertSizing = "bottom";
                  Position = "100 0";
                  Extent = "30 18";
                  MinExtent = "8 8";
                  canSave = "1";
                  Visible = "1";
                  Command = "ETransformSelection.getAbsPosition();";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
                  tooltip = "Get absolute position for selected objects";
                  text = "Get";
                  groupNum = "-1";
                  buttonType = "PushButton";
                  useMouseEvents = "0";
               };
               
               new GuiTextCtrl() {
                  canSaveDynamicFields = "0";
                  Enabled = "1";
                  isContainer = "0";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  position = "20 22";
                  Extent = "20 18";
                  MinExtent = "8 2";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";
                  Margin = "0 0 0 0";
                  Padding = "0 0 0 0";
                  AnchorTop = "1";
                  AnchorBottom = "0";
                  AnchorLeft = "1";
                  AnchorRight = "0";
                  text = "X:";
                  maxLength = "1024";
               };
               
               new GuiTextEditCtrl() {
                  class = "ETransformSelectionTextEdit";
                  internalName = "PosX";
                  profile="ToolsGuiNumericTextEditProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "40 22";
                  Extent = "90 18";
                  text ="0.0";
                  maxLength = "1024";
                  AltCommand = "";
               };
               
               new GuiTextCtrl() {
                  canSaveDynamicFields = "0";
                  Enabled = "1";
                  isContainer = "0";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  position = "20 44";
                  Extent = "20 18";
                  MinExtent = "8 2";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";
                  Margin = "0 0 0 0";
                  Padding = "0 0 0 0";
                  AnchorTop = "1";
                  AnchorBottom = "0";
                  AnchorLeft = "1";
                  AnchorRight = "0";
                  text = "Y:";
                  maxLength = "1024";
               };
               
               new GuiTextEditCtrl() {
                  class = "ETransformSelectionTextEdit";
                  internalName = "PosY";
                  profile="ToolsGuiNumericTextEditProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "40 44";
                  Extent = "90 18";
                  text ="0.0";
                  maxLength = "1024";
                  AltCommand = "";
               };
               
               new GuiTextCtrl() {
                  canSaveDynamicFields = "0";
                  Enabled = "1";
                  isContainer = "0";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  position = "20 66";
                  Extent = "20 18";
                  MinExtent = "8 2";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";
                  Margin = "0 0 0 0";
                  Padding = "0 0 0 0";
                  AnchorTop = "1";
                  AnchorBottom = "0";
                  AnchorLeft = "1";
                  AnchorRight = "0";
                  text = "Z:";
                  maxLength = "1024";
               };
               
               new GuiTextEditCtrl() {
                  class = "ETransformSelectionTextEdit";
                  internalName = "PosZ";
                  profile="ToolsGuiNumericTextEditProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "40 66";
                  Extent = "90 18";
                  text ="0.0";
                  maxLength = "1024";
                  AltCommand = "";
               };
               
               new GuiCheckBoxCtrl(){
                  class = "ETransformSelectionCheckBoxClass";
                  internalName = "PosRelative";
                  Enabled = "1";
                  Profile = "ToolsGuiCheckBoxProfile";
                  position = "40 88";
                  Extent = "120 18";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
                  tooltip = "Add values to current position (checked) or set absolute position (unchecked)";
                  text = "Relative";
                  Command = "";
               };
            };
            
            new GuiBitmapCtrl() {
               Enabled = "1";
               Profile = "ToolsGuiDefaultProfile";
               position = "0 0";
               Extent = "100 2";
               MinExtent = "1 1";
               bitmapAsset = "ToolsModule:separator_v_image";
            };

            new GuiControl() {
               Enabled = "1";
               Profile = "ToolsGuiDefaultProfile";
               HorizSizing = "width";
               VertSizing = "bottom";
               Position = "0 0";
               Extent = "140 128";
               MinExtent = "16 16";
               Visible = "1";
               
               new GuiCheckBoxCtrl(){
                  class = "ETransformSelectionCheckBoxClass";
                  internalName = "DoRotation";
                  Enabled = "1";
                  Profile = "ToolsGuiCheckBoxProfile";
                  position = "1 0";
                  Extent = "190 18";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
                  tooltip = "Apply changes to rotation";
                  text = "Rotation";
                  Command = "";
               };
               
               new GuiButtonCtrl() {
                  class = "ETransformSelectionButtonClass";
                  internalName = "GetRotButton";
                  canSaveDynamicFields = "0";
                  Enabled = "1";
                  isContainer = "0";
                  Profile = "ToolsGuiButtonProfile";
                  HorizSizing = "left";
                  VertSizing = "bottom";
                  Position = "100 0";
                  Extent = "30 18";
                  MinExtent = "8 8";
                  canSave = "1";
                  Visible = "1";
                  Command = "ETransformSelection.getAbsRotation();";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
                  tooltip = "Get absolute rotation for selected objects";
                  text = "Get";
                  groupNum = "-1";
                  buttonType = "PushButton";
                  useMouseEvents = "0";
               };
               
               new GuiTextCtrl() {
                  canSaveDynamicFields = "0";
                  Enabled = "1";
                  isContainer = "0";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  position = "20 22";
                  Extent = "20 18";
                  MinExtent = "8 2";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";
                  Margin = "0 0 0 0";
                  Padding = "0 0 0 0";
                  AnchorTop = "1";
                  AnchorBottom = "0";
                  AnchorLeft = "1";
                  AnchorRight = "0";
                  text = "H:";
                  maxLength = "1024";
               };
               
               new GuiTextEditCtrl() {
                  class = "ETransformSelectionTextEdit";
                  internalName = "Heading";
                  profile="ToolsGuiNumericTextEditProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "40 22";
                  Extent = "90 18";
                  text ="0.0";
                  maxLength = "1024";
                  AltCommand = "";
               };
               
               new GuiTextCtrl() {
                  canSaveDynamicFields = "0";
                  Enabled = "1";
                  isContainer = "0";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  position = "20 44";
                  Extent = "20 18";
                  MinExtent = "8 2";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";
                  Margin = "0 0 0 0";
                  Padding = "0 0 0 0";
                  AnchorTop = "1";
                  AnchorBottom = "0";
                  AnchorLeft = "1";
                  AnchorRight = "0";
                  text = "P:";
                  maxLength = "1024";
               };
               
               new GuiTextEditCtrl() {
                  class = "ETransformSelectionTextEdit";
                  internalName = "Pitch";
                  profile="ToolsGuiNumericTextEditProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "40 44";
                  Extent = "90 18";
                  text ="0.0";
                  maxLength = "1024";
                  AltCommand = "";
               };
               
               new GuiTextCtrl() {
                  canSaveDynamicFields = "0";
                  Enabled = "1";
                  isContainer = "0";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  position = "20 66";
                  Extent = "20 18";
                  MinExtent = "8 2";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";
                  Margin = "0 0 0 0";
                  Padding = "0 0 0 0";
                  AnchorTop = "1";
                  AnchorBottom = "0";
                  AnchorLeft = "1";
                  AnchorRight = "0";
                  text = "B:";
                  maxLength = "1024";
               };
               
               new GuiTextEditCtrl() {
                  class = "ETransformSelectionTextEdit";
                  internalName = "Bank";
                  profile="ToolsGuiNumericTextEditProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "40 66";
                  Extent = "90 18";
                  text ="0.0";
                  maxLength = "1024";
                  AltCommand = "";
               };
               
               new GuiCheckBoxCtrl(){
                  class = "ETransformSelectionCheckBoxClass";
                  internalName = "RotRelative";
                  Enabled = "1";
                  Profile = "ToolsGuiCheckBoxProfile";
                  position = "40 88";
                  Extent = "120 18";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
                  tooltip = "Add values to current rotation (checked) or set absolute rotation (unchecked)";
                  text = "Relative";
                  Command = "ETransformSelection.RotRelativeChanged();";
               };
               
               new GuiCheckBoxCtrl(){
                  class = "ETransformSelectionCheckBoxClass";
                  internalName = "RotLocal";
                  Enabled = "1";
                  Profile = "ToolsGuiCheckBoxProfile";
                  position = "40 110";
                  Extent = "120 18";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
                  tooltip = "Use object's local origin to rotate from";
                  text = "Local Center";
                  Command = "ETransformSelection.RotLocalChanged();";
               };
            };
            
            new GuiBitmapCtrl() {
               Enabled = "1";
               Profile = "ToolsGuiDefaultProfile";
               position = "0 0";
               Extent = "100 2";
               MinExtent = "1 1";
               bitmapAsset = "ToolsModule:separator_v_image";
            };

            new GuiTabBookCtrl() {
               internalName = "ScaleTabBook";
               canSaveDynamicFields = "0";
               isContainer = "1";
               Profile = "ToolsGuiTabBookProfile";
               HorizSizing = "width";
               VertSizing = "bottom";
               position = "0 0";
               Extent = "140 176";
               MinExtent = "16 16";
               canSave = "1";
               Visible = "1";
               hovertime = "1000";
               Margin = "3 2 3 3";
               Padding = "0 0 0 0";
               AnchorTop = "1";
               AnchorBottom = "0";
               AnchorLeft = "1";
               AnchorRight = "0";
               TabPosition = "Top";
               TabMargin = "0";
               MinTabWidth = "50";

               new GuiTabPageCtrl() {
                  canSaveDynamicFields = "0";
                  Enabled = "1";
                  isContainer = "1";
                  Profile = "ToolsGuiTabPageProfile";
                  HorizSizing = "width";
                  VertSizing = "height";
                  position = "0 19";
                  Extent = "140 156";
                  MinExtent = "8 2";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";
                  Margin = "0 0 0 0";
                  Padding = "0 0 0 0";
                  AnchorTop = "1";
                  AnchorBottom = "0";
                  AnchorLeft = "1";
                  AnchorRight = "0";
                  text = "Scale";
                  maxLength = "1024";
         
                  new GuiBitmapBorderCtrl() {
                     canSaveDynamicFields = "0";
                     Enabled = "1";
                     isContainer = "0";
                     Profile = "ToolsGuiTabBorderProfile";
                     HorizSizing = "width";
                     VertSizing = "bottom";
                     position = "0 0";
                     Extent = "134 156";
                     MinExtent = "8 2";
                     canSave = "0";
                     Visible = "1";
                     hovertime = "1000";
                  };
                  
                  new GuiControl() {
                     Enabled = "1";
                     Profile = "ToolsGuiDefaultProfile";
                     HorizSizing = "width";
                     VertSizing = "bottom";
                     Position = "0 0";
                     Extent = "134 156";
                     MinExtent = "16 16";
                     Visible = "1";
                     
                     new GuiCheckBoxCtrl(){
                        class = "ETransformSelectionCheckBoxClass";
                        internalName = "DoScale";
                        Enabled = "1";
                        Profile = "ToolsGuiCheckBoxProfile";
                        position = "2 4";
                        Extent = "100 18";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        tooltip = "Apply changes to scale";
                        text = "Scale";
                        Command = "";
                     };
                     
                     new GuiButtonCtrl() {
                        class = "ETransformSelectionButtonClass";
                        internalName = "GetScaleButton";
                        canSaveDynamicFields = "0";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiButtonProfile";
                        HorizSizing = "left";
                        VertSizing = "bottom";
                        Position = "100 4";
                        Extent = "30 18";
                        MinExtent = "8 8";
                        canSave = "1";
                        Visible = "1";
                        Command = "ETransformSelection.getAbsScale();";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        tooltip = "Get absolute scale for selected objects";
                        text = "Get";
                        groupNum = "-1";
                        buttonType = "PushButton";
                        useMouseEvents = "0";
                     };
                     
                     new GuiTextCtrl() {
                        canSaveDynamicFields = "0";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiDefaultProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        position = "20 26";
                        Extent = "20 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        hovertime = "1000";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        text = "X:";
                        maxLength = "1024";
                     };
                     
                     new GuiTextEditCtrl() {
                        class = "ETransformSelectionTextEdit";
                        internalName = "ScaleX";
                        profile="ToolsGuiNumericTextEditProfile";
                        HorizSizing = "width";
                        VertSizing = "bottom";
                        position = "40 26";
                        Extent = "90 18";
                        text ="1.0";
                        maxLength = "1024";
                        AltCommand = "";
                     };
                     
                     new GuiTextCtrl() {
                        canSaveDynamicFields = "0";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiDefaultProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        position = "20 48";
                        Extent = "20 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        hovertime = "1000";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        text = "Y:";
                        maxLength = "1024";
                     };
                     
                     new GuiTextEditCtrl() {
                        class = "ETransformSelectionTextEdit";
                        internalName = "ScaleY";
                        profile="ToolsGuiNumericTextEditProfile";
                        HorizSizing = "width";
                        VertSizing = "bottom";
                        position = "40 48";
                        Extent = "90 18";
                        text ="1.0";
                        maxLength = "1024";
                        AltCommand = "";
                     };
                     
                     new GuiTextCtrl() {
                        canSaveDynamicFields = "0";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiDefaultProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        position = "20 70";
                        Extent = "20 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        hovertime = "1000";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        text = "Z:";
                        maxLength = "1024";
                     };
                     
                     new GuiTextEditCtrl() {
                        class = "ETransformSelectionTextEdit";
                        internalName = "ScaleZ";
                        profile="ToolsGuiNumericTextEditProfile";
                        HorizSizing = "width";
                        VertSizing = "bottom";
                        position = "40 70";
                        Extent = "90 18";
                        text ="1.0";
                        maxLength = "1024";
                        AltCommand = "";
                     };
                     
                     new GuiCheckBoxCtrl(){
                        class = "ETransformSelectionCheckBoxClass";
                        internalName = "ScaleRelative";
                        Enabled = "1";
                        Profile = "ToolsGuiCheckBoxProfile";
                        position = "40 92";
                        Extent = "120 18";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        tooltip = "Add values to current scale (checked) or set absolute scale (unchecked)";
                        text = "Relative";
                        Command = "";
                     };
                     
                     new GuiCheckBoxCtrl(){
                        class = "ETransformSelectionCheckBoxClass";
                        internalName = "ScaleLocal";
                        Enabled = "1";
                        Profile = "ToolsGuiCheckBoxProfile";
                        position = "40 114";
                        Extent = "120 18";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        tooltip = "Use object's local origin to scale from";
                        text = "Local Center";
                        Command = "";
                     };
                     
                     new GuiCheckBoxCtrl(ETransformSelectionScaleProportional){
                        class = "ETransformSelectionCheckBoxClass";
                        internalName = "ScaleProportional";
                        Enabled = "1";
                        Profile = "ToolsGuiCheckBoxProfile";
                        position = "40 136";
                        Extent = "120 18";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        tooltip = "Scale equally in all directions";
                        text = "Constrain Proportions";
                        Command = "";
                     };
                  };
               };

               new GuiTabPageCtrl() {
                  canSaveDynamicFields = "0";
                  Enabled = "1";
                  isContainer = "1";
                  Profile = "ToolsGuiTabPageProfile";
                  HorizSizing = "width";
                  VertSizing = "height";
                  position = "0 19";
                  Extent = "140 156";
                  MinExtent = "8 2";
                  canSave = "1";
                  Visible = "1";
                  hovertime = "1000";
                  Margin = "0 0 0 0";
                  Padding = "0 0 0 0";
                  AnchorTop = "1";
                  AnchorBottom = "0";
                  AnchorLeft = "1";
                  AnchorRight = "0";
                  text = "Size";
                  maxLength = "1024";
         
                  new GuiBitmapBorderCtrl() {
                     canSaveDynamicFields = "0";
                     Enabled = "1";
                     isContainer = "0";
                     Profile = "ToolsGuiTabBorderProfile";
                     HorizSizing = "width";
                     VertSizing = "bottom";
                     position = "0 0";
                     Extent = "134 156";
                     MinExtent = "8 2";
                     canSave = "0";
                     Visible = "1";
                     hovertime = "1000";
                  };
                  
                  new GuiControl() {
                     Enabled = "1";
                     Profile = "ToolsGuiDefaultProfile";
                     HorizSizing = "width";
                     VertSizing = "bottom";
                     Position = "0 0";
                     Extent = "134 156";
                     MinExtent = "16 16";
                     Visible = "1";
                     
                     new GuiCheckBoxCtrl(){
                        class = "ETransformSelectionCheckBoxClass";
                        internalName = "DoSize";
                        Enabled = "1";
                        Profile = "ToolsGuiCheckBoxProfile";
                        position = "2 4";
                        Extent = "100 18";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        tooltip = "Apply changes to size";
                        text = "Size";
                        Command = "";
                     };
                     
                     new GuiButtonCtrl() {
                        class = "ETransformSelectionButtonClass";
                        internalName = "GetSizeButton";
                        canSaveDynamicFields = "0";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiButtonProfile";
                        HorizSizing = "left";
                        VertSizing = "bottom";
                        Position = "100 4";
                        Extent = "30 18";
                        MinExtent = "8 8";
                        canSave = "1";
                        Visible = "1";
                        Command = "ETransformSelection.getAbsSize();";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        tooltip = "Get absolute size for selected objects";
                        text = "Get";
                        groupNum = "-1";
                        buttonType = "PushButton";
                        useMouseEvents = "0";
                     };
                     
                     new GuiTextCtrl() {
                        canSaveDynamicFields = "0";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiDefaultProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        position = "20 26";
                        Extent = "20 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        hovertime = "1000";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        text = "X:";
                        maxLength = "1024";
                     };
                     
                     new GuiTextEditCtrl() {
                        class = "ETransformSelectionTextEdit";
                        internalName = "SizeX";
                        profile="ToolsGuiNumericTextEditProfile";
                        HorizSizing = "width";
                        VertSizing = "bottom";
                        position = "40 26";
                        Extent = "90 18";
                        text ="1.0";
                        maxLength = "1024";
                        AltCommand = "";
                     };
                     
                     new GuiTextCtrl() {
                        canSaveDynamicFields = "0";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiDefaultProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        position = "20 48";
                        Extent = "20 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        hovertime = "1000";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        text = "Y:";
                        maxLength = "1024";
                     };
                     
                     new GuiTextEditCtrl() {
                        class = "ETransformSelectionTextEdit";
                        internalName = "SizeY";
                        profile="ToolsGuiNumericTextEditProfile";
                        HorizSizing = "width";
                        VertSizing = "bottom";
                        position = "40 48";
                        Extent = "90 18";
                        text ="1.0";
                        maxLength = "1024";
                        AltCommand = "";
                     };
                     
                     new GuiTextCtrl() {
                        canSaveDynamicFields = "0";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiDefaultProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        position = "20 70";
                        Extent = "20 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        hovertime = "1000";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        text = "Z:";
                        maxLength = "1024";
                     };
                     
                     new GuiTextEditCtrl() {
                        class = "ETransformSelectionTextEdit";
                        internalName = "SizeZ";
                        profile="ToolsGuiNumericTextEditProfile";
                        HorizSizing = "width";
                        VertSizing = "bottom";
                        position = "40 70";
                        Extent = "90 18";
                        text ="1.0";
                        maxLength = "1024";
                        AltCommand = "";
                     };
                     
                     new GuiCheckBoxCtrl(){
                        class = "ETransformSelectionCheckBoxClass";
                        internalName = "SizeRelative";
                        Enabled = "1";
                        Profile = "ToolsGuiCheckBoxProfile";
                        position = "40 92";
                        Extent = "120 18";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        tooltip = "Add values to current size (checked) or set absolute size (unchecked)";
                        text = "Relative";
                        Command = "";
                     };
                     
                     new GuiCheckBoxCtrl(){
                        class = "ETransformSelectionCheckBoxClass";
                        internalName = "SizeLocal";
                        Enabled = "1";
                        Profile = "ToolsGuiCheckBoxProfile";
                        position = "40 114";
                        Extent = "120 18";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        tooltip = "Use object's local origin to size from";
                        text = "Local Center";
                        Command = "";
                     };
                     
                     new GuiCheckBoxCtrl(ETransformSelectionSizeProportional){
                        class = "ETransformSelectionCheckBoxClass";
                        internalName = "SizeProportional";
                        Enabled = "1";
                        Profile = "ToolsGuiCheckBoxProfile";
                        position = "40 136";
                        Extent = "120 18";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        tooltip = "Size equally in all directions";
                        text = "Constrain Proportions";
                        Command = "";
                     };
                  };
               };
            };
         };      
      };

      new GuiContainer(){
         HorizSizing = "width";
         VertSizing = "height";
         Position = "0 0";
         Extent = "190 24";
         Docking = "Bottom";
         Margin = "5 5 5 5";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";

         new GuiButtonCtrl() {
            class = "ETransformSelectionButtonClass";
            internalName = "ApplyButton";
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "0 0";
            Extent = "50 23";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "ETransformSelection.apply();";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            text = "Apply";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
         };

         new GuiButtonCtrl() {
            class = "ETransformSelectionButtonClass";
            internalName = "CloseButton";
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "left";
            VertSizing = "bottom";
            Position = "140 0";
            Extent = "50 23";
            MinExtent = "8 8";
            canSave = "1";
            Visible = "1";
            Command = "ETransformSelection.hideDialog();";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            text = "Close";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
         };
      };
      
   };
};
