$paletteId = new GuiControl(RoadEditorPalette,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   
   new GuiBitmapButtonCtrl(ERoadEditorSelectModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RoadEditorSelectMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RoadEditorGui.prepSelectionMode();";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Select Road (1)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:arrow_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   }; 
   
   new GuiBitmapButtonCtrl(ERoadEditorMoveModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RoadEditorMoveMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RoadEditorGui.setMode(\"RoadEditorMoveMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Move Point (2)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:move_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   
   new GuiBitmapButtonCtrl(ERoadEditorScaleModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RoadEditorScaleMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RoadEditorGui.setMode(\"RoadEditorScaleMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Scale Point (4)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:scale_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ERoadEditorAddModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RoadEditorAddRoadMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RoadEditorGui.setMode(\"RoadEditorAddRoadMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Create Road (5)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:add_road_path_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ERoadEditorInsertModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RoadEditorInsertPointMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RoadEditorGui.setMode(\"RoadEditorInsertPointMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Insert Point (+)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:add_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   
   new GuiBitmapButtonCtrl(ERoadEditorRemoveModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "RoadEditorRemovePointMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "RoadEditorGui.setMode(\"RoadEditorRemovePointMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Remove Point (-)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:subtract_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
};
