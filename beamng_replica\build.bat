@echo off
echo Setting up BeamNG Replica...
echo.

REM Set up directories
set TORQUE3D_DIR=..

echo NOTE: C++ compilation requires a C++ compiler (MinGW-w64 or Visual Studio)
echo For now, we'll set up the TorqueScript components only.
echo.

echo The C++ source code is ready for compilation when you have a compiler:
echo - cpp/jbeam_parser/JBeamParser.h/.cpp
echo - cpp/physics/SoftBodyPhysics.h/.cpp
echo.

echo Setting up TorqueScript components...

REM Copy scripts to Torque3D
echo Copying scripts to Torque3D...
if not exist %TORQUE3D_DIR%/beamng_replica mkdir %TORQUE3D_DIR%/beamng_replica
xcopy /E /Y scripts %TORQUE3D_DIR%/beamng_replica/scripts\
xcopy /E /Y levels %TORQUE3D_DIR%/beamng_replica/levels\
xcopy /E /Y jbeam %TORQUE3D_DIR%/beamng_replica/jbeam\

echo.
echo Installation completed!
echo.
echo To run the test:
echo 1. Start Torque3D.exe
echo 2. In console, type: exec("beamng_replica/levels/test_track/main.tscript");
echo 3. Then type: initTestTrack();
echo.
pause
