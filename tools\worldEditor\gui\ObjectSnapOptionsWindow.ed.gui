//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(ObjectSnapOptionsContainer, EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiModelessDialogProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "800 600";
   MinExtent = "8 2";
   canSave = "1";
   isDecoy = "0";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";

   new GuiWindowCollapseCtrl(ESnapOptions) {
      internalName = "SnapOptionsWindow";
      Enabled = "1";
      isContainer = "1";
      profile = "ToolsGuiWindowProfile";
      HorizSizing = "windowRelative";
      VertSizing = "windowRelative";
      resizeWidth = "0";
      resizeHeight = "1";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      position = "400 31";
      extent =" 175 267";
      MinExtent = "175 130";
      text = "Snap Options";
      closeCommand = "ESnapOptions.hideDialog();";
      EdgeSnap = "0";
      canCollapse = "0";
      visible = "0";
      Margin = "5 5 5 5";
      Padding = "5 5 5 5";
               
      new GuiTabBookCtrl(ESnapOptionsTabBook) {
         canSaveDynamicFields = "0";
         isContainer = "1";
         Profile = "ToolsGuiTabBookProfile";
         HorizSizing = "width";
         VertSizing = "height";
         Position = "5 52";
         Extent = "190 240";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         Docking = "Client";
         Margin = "3 32 3 3";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         TabPosition = "Top";
         TabMargin = "10";
         MinTabWidth = "8";

         new GuiTabPageCtrl(ESnapOptionsTabTerrain) {
            canSaveDynamicFields = "0";
            Profile = "ToolsGuiTabPageProfile";
            HorizSizing = "width";
            VertSizing = "height";
            Margin = "0 0 0 0";
            Position = "0 19";
            Extent = "190 220";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "0";
            hovertime = "1000";
            Docking = "None";
            text = "Terrain";
            maxLength = "255";
            command = "toggleSnappingOptions(\"terrain\");";
            
            new GuiScrollCtrl() {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "1";
               Profile = "ToolsGuiScrollProfile";
               HorizSizing = "width";
               VertSizing = "height";
               Docking = "Client";
               Position = "4 12";
               Extent = "156 190";
               MinExtent = "8 2";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               Margin = "0 0 0 0";
               Padding = "0 0 0 0";
               AnchorTop = "1";
               AnchorBottom = "0";
               AnchorLeft = "1";
               AnchorRight = "0";
               willFirstRespond = "1";
               hScrollBar = "alwaysOff";
               vScrollBar = "dynamic";
               lockHorizScroll = "true";
               lockVertScroll = "false";
               constantThumbHeight = "0";
               childMargin = "2 2";

               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  Padding = "0";
                  canSaveDynamicFields = "0";
                  Enabled = "1";
                  isContainer = "1";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  Position = "0 0";
                  Extent = "190 90";
                  MinExtent = "16 16";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
                  
                  new GuiDynamicCtrlArrayControl() {
                     canSaveDynamicFields = "0";
                     internalName = "TerrainSnapAlignment";
                     Enabled = "1";
                     Profile = "ToolsGuiDefaultProfile";
                     HorizSizing = "right";
                     VertSizing = "bottom";
                     Position = "0 18";
                     Extent = "190 72";
                     MinExtent = "16 16";
                     canSave = "1";
                     isDecoy = "0";
                     Visible = "1";
                     tooltipprofile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     colCount = "2";
                     colSize = "78";
                     rowSize = "20";
                     rowSpacing = "2";
                     colSpacing = "2";
                     dynamicSize = true;
                     
                     new GuiTextCtrl() {
                        canSaveDynamicFields = "0";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiDefaultProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        position = "0 0";
                        Extent = "190 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        hovertime = "1000";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        text = "  Alignment:";
                        maxLength = "1024";
                     };
                     new GuiRadioCtrl() {
                        canSaveDynamicFields = "0";
                        internalName = "NoAlignment";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiRadioProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        Position = "0 0";
                        Extent = "40 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        isDecoy = "0";
                        Visible = "1";
                        Command = "ESnapOptions.setTerrainSnapAlignment(\"None\");";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        text = "None";
                        groupNum = "1";
                        buttonType = "RadioButton";
                        useMouseEvents = "0";
                        useInactiveState = "0";
                     };
                     new GuiIconButtonCtrl() {
                        canSaveDynamicFields = "0";
                        internalName = "negX";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiIconButtonSmallProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        Position = "0 0";
                        Extent = "40 20";
                        MinExtent = "8 2";
                        canSave = "1";
                        isDecoy = "0";
                        Visible = "1";
                        Command = "ESnapOptions.setTerrainSnapAlignment(\"-X\");";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        text = "- X Axis";
                        bitmapAsset = "ToolsModule:axis_icon__x_image";
                        textMargin = "24";
                        groupNum = "1";
                        buttonType = "RadioButton";
                        useMouseEvents = "0";
                        useInactiveState = "0";
                     };
                     new GuiIconButtonCtrl() {
                        canSaveDynamicFields = "0";
                        internalName = "posX";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiIconButtonSmallProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        Position = "0 0";
                        Extent = "40 20";
                        MinExtent = "8 2";
                        canSave = "1";
                        isDecoy = "0";
                        Visible = "1";
                        Command = "ESnapOptions.setTerrainSnapAlignment(\"+X\");";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        text = "+ X Axis";
                        bitmapAsset = "ToolsModule:axis_icon_x_image";
                        textMargin = "24";
                        groupNum = "1";
                        buttonType = "RadioButton";
                        useMouseEvents = "0";
                        useInactiveState = "0";
                     };
                     new GuiIconButtonCtrl() {
                        canSaveDynamicFields = "0";
                        internalName = "negY";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiIconButtonSmallProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        Position = "0 0";
                        Extent = "40 20";
                        MinExtent = "8 2";
                        canSave = "1";
                        isDecoy = "0";
                        Visible = "1";
                        Command = "ESnapOptions.setTerrainSnapAlignment(\"-Y\");";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        text = "- Y Axis";
                        bitmapAsset = "ToolsModule:axis_icon__y_image";
                        textMargin = "24";
                        groupNum = "1";
                        buttonType = "RadioButton";
                        useMouseEvents = "0";
                        useInactiveState = "0";
                     };
                     new GuiIconButtonCtrl() {
                        canSaveDynamicFields = "0";
                        internalName = "posY";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiIconButtonSmallProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        Position = "0 0";
                        Extent = "40 20";
                        MinExtent = "8 2";
                        canSave = "1";
                        isDecoy = "0";
                        Visible = "1";
                        Command = "ESnapOptions.setTerrainSnapAlignment(\"+Y\");";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        text = "+ Y Axis";
                        bitmapAsset = "ToolsModule:axis_icon_y_image";
                        textMargin = "24";
                        groupNum = "1";
                        buttonType = "RadioButton";
                        useMouseEvents = "0";
                        useInactiveState = "0";
                     };
                     new GuiIconButtonCtrl() {
                        canSaveDynamicFields = "0";
                        internalName = "negZ";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiIconButtonSmallProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        Position = "0 0";
                        Extent = "40 20";
                        MinExtent = "8 2";
                        canSave = "1";
                        isDecoy = "0";
                        Visible = "1";
                        Command = "ESnapOptions.setTerrainSnapAlignment(\"-Z\");";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        text = "- Z Axis";
                        bitmapAsset = "ToolsModule:axis_icon__z_image";
                        textMargin = "24";
                        groupNum = "1";
                        buttonType = "RadioButton";
                        useMouseEvents = "0";
                        useInactiveState = "0";
                     };
                     new GuiIconButtonCtrl() {
                        canSaveDynamicFields = "0";
                        internalName = "posZ";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiIconButtonSmallProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        Position = "0 0";
                        Extent = "40 20";
                        MinExtent = "8 2";
                        canSave = "1";
                        isDecoy = "0";
                        Visible = "1";
                        Command = "ESnapOptions.setTerrainSnapAlignment(\"+Z\");";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        text = "+ Z Axis";
                        bitmapAsset = "ToolsModule:axis_icon_z_image";
                        textMargin = "24";
                        groupNum = "1";
                        buttonType = "RadioButton";
                        useMouseEvents = "0";
                        useInactiveState = "0";
                     };
                  };
                  new GuiCheckBoxCtrl() {
                     profile = "ToolsGuiCheckBoxProfile";
                     text = "Snap to object bounding box";
                     groupNum = "1";
                     useMouseEvents = "0";
                     isContainer = "0";
                     horizSizing = "right";
                     vertSizing = "top";
                     position = "4 249";
                     extent = "165 24";
                     minExtent = "8 8";
                     visible = "1";
                     active = "1";
                     Variable = "EWorldEditor.dropAtBounds";
                     Command = "EWorldEditor.dropAtBounds = $ThisControl.getValue();";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "0";
                  };
               };
            };
         };
         new GuiTabPageCtrl(ESnapOptionsTabSoft) {
            canSaveDynamicFields = "0";
            Profile = "ToolsGuiTabPageProfile";
            HorizSizing = "width";
            VertSizing = "height";
            Margin = "0 0 0 0";
            Position = "0 19";
            Extent = "190 220";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "0";
            hovertime = "1000";
            Docking = "None";
            text = "Soft";
            maxLength = "255";
            command = "toggleSnappingOptions(\"soft\");";
            
            new GuiScrollCtrl() {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "1";
               Profile = "ToolsGuiScrollProfile";
               HorizSizing = "width";
               VertSizing = "height";
               Docking = "Client";
               Position = "4 12";
               Extent = "186 190";
               MinExtent = "8 2";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               Margin = "0 0 0 0";
               Padding = "0 0 0 0";
               AnchorTop = "1";
               AnchorBottom = "0";
               AnchorLeft = "1";
               AnchorRight = "0";
               willFirstRespond = "1";
               hScrollBar = "alwaysOff";
               vScrollBar = "dynamic";
               lockHorizScroll = "true";
               lockVertScroll = "false";
               constantThumbHeight = "0";
               childMargin = "2 2";

               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  Padding = "5";
                  canSaveDynamicFields = "0";
                  internalName = "theVisOptionsList";
                  Enabled = "1";
                  isContainer = "0";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  Position = "2 2";
                  Extent = "190 190";
                  MinExtent = "16 16";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
                  
                  new GuiControl() {
                     Enabled = "1";
                     Profile = "ToolsGuiDefaultProfile";
                     HorizSizing = "width";
                     Position = "0 0";
                     Extent = "190 18";
                     MinExtent = "16 16";
                     Visible = "1";
                     
                     new GuiTextCtrl() {
                        canSaveDynamicFields = "0";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiDefaultProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        position = "0 0";
                        Extent = "90 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        hovertime = "1000";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        text = "Snap Size:";
                        maxLength = "1024";
                     };
                     
                     new GuiTextEditCtrl() {
                        internalName = "SnapSize";
                        profile="ToolsGuiNumericTextEditProfile";
                        HorizSizing = "left";
                        position = "136 0";
                        Extent = "44 18";
                        text ="2.0";
                        maxLength = "6";
                        AltCommand = "ESnapOptions.setSoftSnapSize();";
                     };
                  };
                  
                  new GuiControl() {
                     Enabled = "1";
                     Profile = "ToolsGuiDefaultProfile";
                     Position = "0 0";
                     Extent = "190 90";
                     MinExtent = "16 16";
                     Visible = "1";
                  
                     new GuiDynamicCtrlArrayControl() {
                        canSaveDynamicFields = "0";
                        internalName = "SoftSnapAlignment";
                        Enabled = "1";
                        Profile = "ToolsGuiDefaultProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        Position = "0 0";
                        Extent = "190 90";
                        MinExtent = "16 16";
                        canSave = "1";
                        isDecoy = "0";
                        Visible = "1";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        colCount = "2";
                        colSize = "78";
                        rowSize = "20";
                        rowSpacing = "2";
                        colSpacing = "2";
                        
                        new GuiTextCtrl() {
                           canSaveDynamicFields = "0";
                           Enabled = "1";
                           isContainer = "0";
                           Profile = "ToolsGuiDefaultProfile";
                           HorizSizing = "right";
                           VertSizing = "bottom";
                           position = "0 0";
                           Extent = "190 18";
                           MinExtent = "8 2";
                           canSave = "1";
                           Visible = "1";
                           hovertime = "1000";
                           Margin = "0 0 0 0";
                           Padding = "0 0 0 0";
                           AnchorTop = "1";
                           AnchorBottom = "0";
                           AnchorLeft = "1";
                           AnchorRight = "0";
                           text = "  Alignment:";
                           maxLength = "1024";
                        };
                        new GuiRadioCtrl() {
                           canSaveDynamicFields = "0";
                           internalName = "NoAlignment";
                           Enabled = "1";
                           isContainer = "0";
                           Profile = "ToolsGuiRadioProfile";
                           HorizSizing = "right";
                           VertSizing = "bottom";
                           Position = "0 0";
                           Extent = "40 18";
                           MinExtent = "8 2";
                           canSave = "1";
                           isDecoy = "0";
                           Visible = "1";
                           Command = "ESnapOptions.setSoftSnapAlignment(\"None\");";
                           tooltipprofile = "ToolsGuiToolTipProfile";
                           hovertime = "1000";
                           text = "None";
                           groupNum = "1";
                           buttonType = "RadioButton";
                           useMouseEvents = "0";
                           useInactiveState = "0";
                        };
                        new GuiIconButtonCtrl() {
                           canSaveDynamicFields = "0";
                           internalName = "negX";
                           Enabled = "1";
                           isContainer = "0";
                           Profile = "ToolsGuiIconButtonSmallProfile";
                           HorizSizing = "right";
                           VertSizing = "bottom";
                           Position = "0 0";
                           Extent = "40 20";
                           MinExtent = "8 2";
                           canSave = "1";
                           isDecoy = "0";
                           Visible = "1";
                           Command = "ESnapOptions.setSoftSnapAlignment(\"-X\");";
                           tooltipprofile = "ToolsGuiToolTipProfile";
                           hovertime = "1000";
                           text = "- X Axis";
                           bitmapAsset = "ToolsModule:axis_icon__x_image";
                           textMargin = "24";
                           groupNum = "1";
                           buttonType = "RadioButton";
                           useMouseEvents = "0";
                           useInactiveState = "0";
                        };
                        new GuiIconButtonCtrl() {
                           canSaveDynamicFields = "0";
                           internalName = "posX";
                           Enabled = "1";
                           isContainer = "0";
                           Profile = "ToolsGuiIconButtonSmallProfile";
                           HorizSizing = "right";
                           VertSizing = "bottom";
                           Position = "0 0";
                           Extent = "40 20";
                           MinExtent = "8 2";
                           canSave = "1";
                           isDecoy = "0";
                           Visible = "1";
                           Command = "ESnapOptions.setSoftSnapAlignment(\"+X\");";
                           tooltipprofile = "ToolsGuiToolTipProfile";
                           hovertime = "1000";
                           text = "+ X Axis";
                           bitmapAsset = "ToolsModule:axis_icon_x_image";
                           textMargin = "24";
                           groupNum = "1";
                           buttonType = "RadioButton";
                           useMouseEvents = "0";
                           useInactiveState = "0";
                        };
                        new GuiIconButtonCtrl() {
                           canSaveDynamicFields = "0";
                           internalName = "negY";
                           Enabled = "1";
                           isContainer = "0";
                           Profile = "ToolsGuiIconButtonSmallProfile";
                           HorizSizing = "right";
                           VertSizing = "bottom";
                           Position = "0 0";
                           Extent = "40 20";
                           MinExtent = "8 2";
                           canSave = "1";
                           isDecoy = "0";
                           Visible = "1";
                           Command = "ESnapOptions.setSoftSnapAlignment(\"-Y\");";
                           tooltipprofile = "ToolsGuiToolTipProfile";
                           hovertime = "1000";
                           text = "- Y Axis";
                           bitmapAsset = "ToolsModule:axis_icon__y_image";
                           textMargin = "24";
                           groupNum = "1";
                           buttonType = "RadioButton";
                           useMouseEvents = "0";
                           useInactiveState = "0";
                        };
                        new GuiIconButtonCtrl() {
                           canSaveDynamicFields = "0";
                           internalName = "posY";
                           Enabled = "1";
                           isContainer = "0";
                           Profile = "ToolsGuiIconButtonSmallProfile";
                           HorizSizing = "right";
                           VertSizing = "bottom";
                           Position = "0 0";
                           Extent = "40 20";
                           MinExtent = "8 2";
                           canSave = "1";
                           isDecoy = "0";
                           Visible = "1";
                           Command = "ESnapOptions.setSoftSnapAlignment(\"+Y\");";
                           tooltipprofile = "ToolsGuiToolTipProfile";
                           hovertime = "1000";
                           text = "+ Y Axis";
                           bitmapAsset = "ToolsModule:axis_icon_y_image";
                           textMargin = "24";
                           groupNum = "1";
                           buttonType = "RadioButton";
                           useMouseEvents = "0";
                           useInactiveState = "0";
                        };
                        new GuiIconButtonCtrl() {
                           canSaveDynamicFields = "0";
                           internalName = "negZ";
                           Enabled = "1";
                           isContainer = "0";
                           Profile = "ToolsGuiIconButtonSmallProfile";
                           HorizSizing = "right";
                           VertSizing = "bottom";
                           Position = "0 0";
                           Extent = "40 20";
                           MinExtent = "8 2";
                           canSave = "1";
                           isDecoy = "0";
                           Visible = "1";
                           Command = "ESnapOptions.setSoftSnapAlignment(\"-Z\");";
                           tooltipprofile = "ToolsGuiToolTipProfile";
                           hovertime = "1000";
                           text = "- Z Axis";
                           bitmapAsset = "ToolsModule:axis_icon__z_image";
                           textMargin = "24";
                           groupNum = "1";
                           buttonType = "RadioButton";
                           useMouseEvents = "0";
                           useInactiveState = "0";
                        };
                        new GuiIconButtonCtrl() {
                           canSaveDynamicFields = "0";
                           internalName = "posZ";
                           Enabled = "1";
                           isContainer = "0";
                           Profile = "ToolsGuiIconButtonSmallProfile";
                           HorizSizing = "right";
                           VertSizing = "bottom";
                           Position = "0 0";
                           Extent = "40 20";
                           MinExtent = "8 2";
                           canSave = "1";
                           isDecoy = "0";
                           Visible = "1";
                           Command = "ESnapOptions.setSoftSnapAlignment(\"+Z\");";
                           tooltipprofile = "ToolsGuiToolTipProfile";
                           hovertime = "1000";
                           text = "+ Z Axis";
                           bitmapAsset = "ToolsModule:axis_icon_z_image";
                           textMargin = "24";
                           groupNum = "1";
                           buttonType = "RadioButton";
                           useMouseEvents = "0";
                           useInactiveState = "0";
                        };
                     };
                  };
                  
                  new GuiCheckBoxCtrl(){
                     internalName = "RenderSnapBounds";
                     Enabled = "1";
                     Profile = "ToolsGuiCheckBoxProfile";
                     position = "1 0";
                     Extent = "190 18";
                     text = "Render Snap Bounds";
                     Command = "ESnapOptions.toggleRenderSnapBounds();";
                  };
                  
                  new GuiCheckBoxCtrl(){
                     internalName = "RenderSnappedTriangle";
                     Enabled = "1";
                     Profile = "ToolsGuiCheckBoxProfile";
                     position = "1 0";
                     Extent = "190 18";
                     text = "Render Snapped Triangle";
                     Command = "ESnapOptions.toggleRenderSnappedTriangle();";
                  };
                  
                  new GuiControl() {
                     Enabled = "1";
                     Profile = "ToolsGuiDefaultProfile";
                     HorizSizing = "width";
                     Position = "0 0";
                     Extent = "190 18";
                     MinExtent = "16 16";
                     Visible = "1";
                     
                     new GuiTextCtrl() {
                        canSaveDynamicFields = "0";
                        Enabled = "1";
                        isContainer = "0";
                        Profile = "ToolsGuiDefaultProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        position = "0 0";
                        Extent = "110 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        hovertime = "1000";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        text = "Backface Tolerance:";
                        maxLength = "1024";
                     };
                     
                     new GuiTextEditCtrl() {
                        internalName = "SnapBackfaceTolerance";
                        profile="ToolsGuiNumericTextEditProfile";
                        HorizSizing = "left";
                        position = "136 0";
                        Extent = "44 18";
                        text ="0.5";
                        maxLength = "6";
                        AltCommand = "ESnapOptions.getSoftSnapBackfaceTolerance();";
                     };
                  };
               };
            };
         };
      };
      new GuiCheckBoxCtrl() {
            profile = "ToolsGuiCheckBoxProfile";
            text = "Grid Snapping";
            groupNum = "1";
            useMouseEvents = "0";
            isContainer = "0";
            horizSizing = "right";
            vertSizing = "top";
            position = "4 231";
            extent = "95 24";
            minExtent = "8 8";
            visible = "1";
            active = "1";
            command = "toggleSnappingOptions(\"grid\");";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            internalName = "GridSnapButton";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
      new GuiCheckBoxCtrl() {
            profile = "ToolsGuiCheckBoxProfile";
            text = "Use Group Center";
            groupNum = "1";
            useMouseEvents = "0";
            isContainer = "0";
            horizSizing = "right";
            vertSizing = "top";
            position = "4 246";
            extent = "105 24";
            minExtent = "8 8";
            visible = "1";
            active = "1";
            command = "toggleSnappingOptions(\"byGroup\");";
            tooltipProfile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            internalName = "GroupSnapButton";
            canSave = "1";
            canSaveDynamicFields = "0";
         };
      new GuiTextCtrl() {
         text = "Size";
         maxLength = "1024";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         isContainer = "0";
         profile = "ToolsGuiDefaultProfile";
         horizSizing = "left";
         vertSizing = "top";
         position = "103 234";
         extent = "25 18";
         minExtent = "8 2";
         visible = "1";
         active = "1";
         tooltipProfile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
      new GuiTextEditCtrl() {
         historySize = "0";
         password = "0";
         tabComplete = "0";
         sinkAllKeyEvents = "0";
         passwordMask = "�";
         text = "2.0";
         maxLength = "6";
         margin = "0 0 0 0";
         padding = "0 0 0 0";
         anchorTop = "1";
         anchorBottom = "0";
         anchorLeft = "1";
         anchorRight = "0";
         isContainer = "0";
         profile = "ToolsGuiNumericTextEditProfile";
         horizSizing = "left";
         vertSizing = "top";
         position = "127 235";
         extent = "44 18";
         minExtent = "8 2";
         visible = "1";
         active = "1";
         altCommand = "ESnapOptions.setGridSnapSize();";
         tooltipProfile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         internalName = "gridSize";
         canSave = "1";
         canSaveDynamicFields = "0";
      };
      new GuiButtonCtrl() {
         internalName = "NoSnapButton";
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "bottom";
         Position = "133 23";
         Extent = "38 18";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Command = "toggleSnappingOptions(\"\");";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         text = "Off";
         groupNum = "1";
         buttonType = "ToggleButton";
         useMouseEvents = "0";
      };
   };
};
