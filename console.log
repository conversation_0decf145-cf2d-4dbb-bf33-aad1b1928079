//-------------------------- 7/5/2025 -- 11:57:26 -----
Processor Init:
   Processor: 12th Gen Intel(R) Core(TM) i3-12100F
      MMX detected
      SSE detected
      SSE2 detected
      SSE3 detected
      SSE3ex detected 
      SSE4.1 detected
      SSE4.2 detected
      AVX detected
   MultiCore CPU detected [4 cores, 8 logical]
 
Math Init:
   Installing Standard C extensions
   Installing Assembly extensions
   Installing FPU extensions
   Installing MMX extensions
   Installing SSE extensions
 
Initializing platform...
Input Init:

Done
SFXALProvider - No valid devices found!
DebugDrawer Enabled!
GFX Init:
   Null device found
   Direct 3D (version 11.x) device found
   OpenGL device found

Console trace disabled.
AssetManager::addModuleDeclaredAssets() - No assets found at location 'core/utility/' with extension 'asset.taml'.
AssetManager::addModuleDeclaredAssets() - No assets found at location 'core/lighting/' with extension 'asset.taml'.
AssetManager::addModuleDeclaredAssets() - No assets found at location 'core/sfx/' with extension 'asset.taml'.

--------- Parsing Arguments ---------
Attempting to create GFX device: NVIDIA GeForce GTX 1650 (D3D11) []
Device created, setting adapter and enumerating modes
Hardware occlusion query detected: Yes
WMIVideoInfo: DxDiag initialized
Initializing GFXCardProfiler (Direct3D11)
   o Chipset : 'NVIDIA'
   o Card    : 'NVIDIA GeForce GTX 1650'
   o Version : ''
   o VRAM    : 3935 MB
   - Scanning card capabilities...
GFXCardProfiler (Direct3D11) - Setting capability 'maxTextureWidth' to 16384.
GFXCardProfiler (Direct3D11) - Setting capability 'maxTextureHeight' to 16384.
GFXCardProfiler (Direct3D11) - Setting capability 'maxTextureSize' to 16384.
   - Loading card profiles...
--------------
Attempting to set resolution to "1920 1080 0 32 74 4"
Accepted Mode: 
--Resolution     : 1920 1080
--Screen Mode    : Borderless
--Bits Per Pixel : 32
--Refresh Rate   : 74
--Anit-Aliasing Type     : SMAA High
--------------

sfxStartup...
SFXSystem::createDevice - created Null device 'Null Device'
   Provider: Null
   Device: SFX Null Device
   Hardware: No
   Max Buffers: 16
 
UDP initialized on ipv4 IP:0.0.0.0:64455
Asset Manager: Encountered asset Id 'ToolsModule:EditorSettingsWindow,EditorGuiGroup' in asset file 'tools/worldEditor/gui/EditorSettingsWindow,EditorGuiGroup.asset.taml' but it conflicts with existing asset Id in asset file 'tools/gui/EditorSettingsWindow,EditorGuiGroup.asset.taml'.
AssetManager::addModuleDeclaredAssets() - No assets found at location 'core/clientServer/' with extension 'asset.taml'.

--------- Initializing Directory: scripts ---------

--------- Initializing Torque3D: Client Scripts ---------

Initializing Lighting Systems
RenderParticleMgr::_initShader - failed to locate shader ParticlesShaderData!
RenderParticleMgr::_initShader - failed to locate shader OffscreenParticleCompositeShaderData!
--------------
Attempting to set resolution to "1920 1080 0 32 74 4"
Accepted Mode: 
--Resolution     : 1920 1080
--Screen Mode    : Borderless
--Bits Per Pixel : 32
--Refresh Rate   : 74
--Anit-Aliasing Type     : SMAA High
--------------
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXDefaultGUIProfile,GFXGuiCursorProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXDefaultGUIProfile,GFXGuiCursorProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXGuiCursorProfile,GFXDefaultGUIProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXGuiCursorProfile,GFXDefaultGUIProfile) 
MaterialAsset: Unable to find the Material UnderWaterBasicShader
Asset Manager: > Failed to acquire asset Id 'Core_Rendering:NoMaterial' as loading the asset file failed to return the asset or the correct asset type: 'core/rendering/materials/NoMaterial.asset.taml'.
ImageAsset::getAssetById - Finding of asset with id Core_Rendering:NoMaterial failed with no fallback asset
Material(ReflectProbePreviewMat)::_setDiffuseMap(0) - Couldn't load image "Core_Rendering:NoMaterial"
MaterialAsset: Unable to find the Material UnderWaterShader
SimObject::onTamlCustomRead() - Encountered an unknown custom field name of 'emissive'.
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXDefaultGUIProfile,GFXStaticTextureSRGBProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXDefaultGUIProfile,GFXStaticTextureSRGBProfile) 
SimObject::onTamlCustomRead() - Encountered an unknown custom field name of 'emissive'.
SimObject::onTamlCustomRead() - Encountered an unknown custom field name of 'emissive'.
MaterialAsset: Unable to find the Material materialEd_previewMaterial
SimObject::onTamlCustomRead() - Encountered an unknown custom field name of 'emissive'.
MaterialAsset: Unable to find the Material WaterBasicShader
Warning, overwriting material for: NoMaterial
MaterialAsset: Unable to find the Material materialEd_justAlphaMaterial
SimObject::onTamlCustomRead() - Encountered an unknown custom field name of 'emissive'.
Activating Input...
Engine initialized...
Window focus status changed: focus: 1
AntiAliasing has been disabled; it is not compatible with AdvancedLighting.
 % - Initializing Tools
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXStaticTextureSRGBProfile,GFXGuiCursorProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXStaticTextureSRGBProfile,GFXGuiCursorProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXGuiCursorProfile,GFXDefaultGUIProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXGuiCursorProfile,GFXDefaultGUIProfile) 
 % - Initializing Tools Base
 % - Initializing Base Editor
 % - Initializing World Editor
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXDefaultGUIProfile,GFXGuiCursorProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXDefaultGUIProfile,GFXGuiCursorProfile) 
 % - Initializing Asset Browser
TamlXmlParser::parse() - Could not open filename 'tools/assetBrowser/searchCollectionSets.xml' for parse.
 % - Initializing Sketch Tool
 - Initializing Datablock Editor
 % - Initializing Debugger
 % - Initializing Decal Editor
 % - Initializing Forest Editor
 % - Initializing Gui Editor
 % - Initializing Material Editor
Set::add: Object "matEd_cubemapEditor" doesn't exist to add to EditorGui
 % - Initializing Mesh Road Editor
 % - Initializing Mission Area Editor
GFXTextureManager::_lookupTexture: Cached texture tools/missionAreaEditor/images/DefaultHandle has different profile flags: (GFXStaticTextureSRGBProfile,GFXDefaultGUIProfile) 
GFXTextureManager::_lookupTexture: Cached texture tools/missionAreaEditor/images/DefaultHandle has different profile flags: (GFXStaticTextureSRGBProfile,GFXDefaultGUIProfile) 
 % - Initializing Navigation Editor
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXGuiCursorProfile,GFXDefaultGUIProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXGuiCursorProfile,GFXDefaultGUIProfile) 
 % - Initializing Particle Editor
 % - Initializing Physics Tools
No physics plugin exists.
 % - Initializing Project Importer
 % - Initializing River Editor
 - Initializing Road and Path Editor
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXDefaultGUIProfile,GFXGuiCursorProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXDefaultGUIProfile,GFXGuiCursorProfile) 
 % - Initializing Shape Editor
Missing file: tools/Controller/main.tscript!
Missing file: tools/Events/main.tscript!
Missing file: tools/Groups/main.tscript!
Missing file: tools/Inspector/main.tscript!
Missing file: tools/Fields/main.tscript!
Missing file: tools/Tracks/main.tscript!
Missing file: tools/Torque/main.tscript!
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXGuiCursorProfile,GFXDefaultGUIProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXGuiCursorProfile,GFXDefaultGUIProfile) 
Module Manager: Found module: 'tools/tools.module' but it is in a module group 'tools' which has already been loaded.
Module Manager: Cannot load group 'tools' as it is already loaded.

--------- Initializing Torque3D: Server Scripts ---------
Exporting server prefs...
Starting multiplayer mode
UDP initialized on ipv4 IP:0.0.0.0:28000
Asset Manager: Failed to acquire asset Id 'Core_GameObjects:Camera' as it does not exist.
[ASSIMP] Attempting to load file: core/gameObjects/shapes/camera.fbx
[Assimp log message] Info,  T15308: Load core/gameObjects/shapes/camera.fbx
[Assimp log message] Info,  T15308: Found a matching importer for this file format: Autodesk FBX Importer.
[Assimp log message] Info,  T15308: Import root directory is 'core/gameObjects/shapes\'
[Assimp log message] Info,  T15308: Entering post processing pipeline
[Assimp log message] Info,  T15308: JoinVerticesProcess finished | Verts in: 393 out: 233 | ~40.7125
[Assimp log message] Info,  T15308: Leaving post processing pipeline
[ASSIMP] Mesh Count: 1
[ASSIMP] Material Count: 1
[ASSIMP] Loading Material: CameraMat
[ASSIMP] Node Created: RootNode, Parent: ROOT
[ASSIMP] Node Created: camera2, Parent: RootNode
[ASSIMP] Mesh Created: camera2
Object mesh "camera2" has no matching detail ("detail2" has been added automatically)
[ASSIMP] Shape created successfully.
Writing cached shape to core/gameObjects/shapes/camera.cached.dts
*** LOADING MISSION: DefaultEditorLevel
*** Stage 1 load
Asset Manager: Failed to acquire asset Id 'FPSGameplay:soldier_rigged' as it does not exist.
*** Stage 2 load
*** Stage 3 load
*** Mission loaded
Connect request from: IP:0.0.0.0
Connection established 20303
CADD: 20304 local
<<<< saving server datablock cache >>>>
    <<<< sending CRC to client: 722828578 >>>>
*** Sending mission load to client: ToolsModule:DefaultEditorLevel
Time spent in toggleEditor() : 0.206 s
onServerMessage: 
onServerMessage: 
onServerMessage: 
*** New Mission: DefaultEditorLevel
*** Phase 1: Download Datablocks & Targets
% - PostFX Manager - Executing core/postFX/scripts/default.postfxpreset.tscript
HDR FORMAT: GFXFormatR16G16B16A16F
<<<< client datablock cache does not exist, datablocks will be transmitted and cached. >>>>
Sending heartbeat to master server [IP:**************:5664]
*** Phase 2: Download Ghost Objects
Ghost Always objects received.
Client Replication Startup has Happened!
*** Phase 3: Mission Lighting
Mission lighting done
core/utility/scripts/gameObjectManagement.tscript (83): Unknown command onRemove.
  Object Observer(15) Observer -> CameraData -> ShapeBaseData -> GameBaseData -> SimDataBlock -> SimObject
core/utility/scripts/gameObjectManagement.tscript (84): Unknown command onAdd.
  Object Observer(15) Observer -> CameraData -> ShapeBaseData -> GameBaseData -> SimDataBlock -> SimObject
*** Initial Control Object
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXDefaultGUIProfile,GFXStaticTextureSRGBProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXDefaultGUIProfile,GFXStaticTextureSRGBProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXStaticTextureSRGBProfile,GFXDefaultGUIProfile) 
GFXTextureManager::_lookupTexture: Cached texture core/rendering/images/missingTexture has different profile flags: (GFXStaticTextureSRGBProfile,GFXDefaultGUIProfile) 
Could not create a description for binding: +
Could not create a description for binding: +
Time spent in toggleEditor() : 1.341 s
*** Control Object Changed
Received info request from a master server [IP:**************:5664].
AssetBrowser::populatePreviewImages() - Previews to generate: 0
Window focus status changed: focus: 0
  Using background sleep time: 200
Window focus status changed: focus: 1
Set::remove: Object "20346" does not exist in set WorldEditorMenubar
*** ENDING MISSION
Exporting server prefs...
Exporting client prefs
Exporting server prefs
