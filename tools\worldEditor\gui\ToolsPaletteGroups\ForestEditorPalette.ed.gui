$paletteId = new GuiControl(ForestEditorPalette,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   
   new GuiBitmapButtonCtrl(ForestEditorSelectModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "ForestEditorSelectMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "GlobalGizmoProfile.mode = \"None\"; ForestEditorGui.setActiveTool(ForestTools->SelectionTool);";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Select Item (1)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:arrow_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };    
   new GuiBitmapButtonCtrl(ForestEditorMoveModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "ForestEditorMoveMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "GlobalGizmoProfile.mode = \"Move\"; ForestEditorGui.setActiveTool(ForestTools->SelectionTool);";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Move Item (2)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:translate_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ForestEditorRotateModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "ForestEditorRotateMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "GlobalGizmoProfile.mode = \"Rotate\"; ForestEditorGui.setActiveTool(ForestTools->SelectionTool);";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Rotate Item (3)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:rotate_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ForestEditorScaleModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "ForestEditorScaleMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "GlobalGizmoProfile.mode = \"Scale\"; ForestEditorGui.setActiveTool(ForestTools->SelectionTool);";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Scale Item (4)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:scale_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ForestEditorPaintModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "ForestEditorPaintMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "ForestEditorGui.setActiveTool( ForestTools->BrushTool ); ForestTools->BrushTool.mode = \"Paint\";";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Paint (5)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:paint_forest_btn_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(ForestEditorEraseModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "ForestEditorEraseMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "ForestEditorGui.setActiveTool( ForestTools->BrushTool ); ForestTools->BrushTool.mode = \"Erase\";";      
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Erase (6)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:erase_all_btn_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   
   new GuiBitmapButtonCtrl(ForestEditorEraseSelectedModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "ForestEditorEraseSelectedMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "ForestEditorGui.setActiveTool( ForestTools->BrushTool ); ForestTools->BrushTool.mode = \"EraseSelected\";";  
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Erase Selected (7)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:erase_element_btn_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
};
