//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(VerveEditorImportPathNodesGUI) {
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 8";
   canSave = "1";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";
   canSaveDynamicFields = "1";

   new GuiWindowCtrl() {
      resizeWidth = "0";
      resizeHeight = "0";
      canMove = "1";
      canClose = "0";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "50 50";
      EdgeSnap = "1";
      text = "Import Path Nodes";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      HorizSizing = "center";
      VertSizing = "center";
      Position = "268 181";
      Extent = "280 98";
      MinExtent = "256 8";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      canSaveDynamicFields = "0";

      new GuiTextCtrl() {
         text = "Speed:";
         maxLength = "1024";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         isContainer = "0";
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "14 30";
         Extent = "84 16";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";
      };
      new GuiTextEditCtrl(VerveEditorImportPathNodesSpeed) {
         historySize = "0";
         password = "0";
         tabComplete = "0";
         sinkAllKeyEvents = "0";
         passwordMask = "*";
         maxLength = "1024";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         isContainer = "0";
         Profile = "ToolsGuiTextEditProfile";
         HorizSizing = "width";
         VertSizing = "bottom";
         Position = "79 29";
         Extent = "191 18";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";
      };
      new GuiButtonCtrl() {
         text = "Create";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "66 62";
         Extent = "96 24";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Command = "VMotionTrack::_ImportPathNodes( VerveEditorImportPathNodesSpeed.getText() );";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";
      };
      new GuiButtonCtrl() {
         text = "Cancel";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "174 62";
         Extent = "96 24";
         MinExtent = "8 8";
         canSave = "1";
         Visible = "1";
         Command = "VerveEditorWindow.popDialog( VerveEditorImportPathNodesGUI );";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";
      };
   };
};
//--- OBJECT WRITE END ---