//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(VisibilityLayerContainer, EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiModelessDialogProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "800 600";
   MinExtent = "8 2";
   canSave = "1";
   isDecoy = "0";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";

   new GuiWindowCtrl(EVisibility) {
      internalName = "VisibilityLayerWindow";
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiToolbarWindowProfile";
      HorizSizing = "windowRelative";
      VertSizing = "windowRelative";
      Position = getWord(visibilityToggleBtn.position, 0) SPC getWord(EditorGuiToolbar.extent, 1);
      Extent = "161 250"; //175 696 = full length
      MinExtent = "161 86";
      canSave = "1";
      isDecoy = "0";
      Visible = "1";
      hovertime = "1000";
      Docking = "None";
      Margin = "4 4 4 4";
      Padding = "0 0 0 0";
      AnchorTop = "0";
      AnchorBottom = "0";
      AnchorLeft = "0";
      AnchorRight = "0";
      resizeWidth = "1";
      resizeHeight = "1";
      canMove = "1";
      canClose = "0";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "152 300";
      closeCommand = "";
      EdgeSnap = "1";
      text = "";
      
      new GuiTabBookCtrl(EVisibilityTabBook) {
         canSaveDynamicFields = "0";
         Profile = "ToolsGuiTabBookProfile";
         HorizSizing = "width";
         VertSizing = "height";
         Docking = "Client";
         Margin = "3 1 3 3";
         Position = "5 24";
         Extent = "170 226";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";
         TabPosition = "Top";
         TabHeight = "22";
         TabMargin = "7";
         MinTabWidth = "8";

         new GuiTabPageCtrl() {
            canSaveDynamicFields = "0";
            Profile = "ToolsGuiTabPageProfile";
            HorizSizing = "width";
            VertSizing = "height";
            Docking = "Client";
            Margin = "-1 0 0 0";
            Position = "0 14";
            Extent = "164 220";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "0";
            hovertime = "1000";
            text = "Visible";
            maxLength = "255";
            
            new GuiScrollCtrl() {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "1";
               Profile = "ToolsGuiScrollProfile";
               HorizSizing = "width";
               VertSizing = "height";
               Docking = "Client";
               Position = "4 12";
               Extent = "156 190";
               MinExtent = "8 2";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               Margin = "0 0 0 0";
               Padding = "0 0 0 0";
               AnchorTop = "1";
               AnchorBottom = "0";
               AnchorLeft = "1";
               AnchorRight = "0";
               willFirstRespond = "1";
               hScrollBar = "alwaysOff";
               vScrollBar = "dynamic";
               lockHorizScroll = "true";
               lockVertScroll = "false";
               constantThumbHeight = "0";
               childMargin = "2 0";

               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  Padding = "-2";
                  canSaveDynamicFields = "0";
                  internalName = "theClassVisList";
                  Enabled = "1";
                  isContainer = "1";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  Position = "1 0";
                  Extent = "156 16";
                  MinExtent = "16 16";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
               };
            };
         };
         new GuiTabPageCtrl() {
            canSaveDynamicFields = "0";
            Profile = "ToolsGuiTabPageProfile";
            HorizSizing = "width";
            VertSizing = "height";
            Docking = "Client";
            Margin = "-1 0 0 0";
            Position = "0 14";
            Extent = "164 220";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "0";
            hovertime = "1000";
            text = "Select";
            maxLength = "255";
            
            new GuiScrollCtrl() {
               canSaveDynamicFields = "0";
               Enabled = "1";
               isContainer = "1";
               Profile = "ToolsGuiScrollProfile";
               HorizSizing = "width";
               VertSizing = "height";
               Docking = "Client";
               Position = "4 12";
               Extent = "156 190";
               MinExtent = "8 2";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               Margin = "0 0 0 0";
               Padding = "0 0 0 0";
               AnchorTop = "1";
               AnchorBottom = "0";
               AnchorLeft = "1";
               AnchorRight = "0";
               willFirstRespond = "1";
               hScrollBar = "alwaysOff";
               vScrollBar = "dynamic";
               lockHorizScroll = "true";
               lockVertScroll = "false";
               constantThumbHeight = "0";
               childMargin = "2 0";

               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  Padding = "-2";
                  canSaveDynamicFields = "0";
                  internalName = "theClassSelList";
                  Enabled = "1";
                  isContainer = "1";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  Position = "1 0";
                  Extent = "156 16";
                  MinExtent = "16 16";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000";
               };
            };
         };
      };
   };
};