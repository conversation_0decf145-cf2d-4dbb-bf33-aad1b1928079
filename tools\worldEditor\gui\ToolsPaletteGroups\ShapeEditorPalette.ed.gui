$paletteId = new GuiControl(ShapeEditorPalette,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   
   new GuiBitmapButtonCtrl(ShapeEditorNoneModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "WorldEditorSelectArrow";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "GlobalGizmoProfile.mode = \"None\";";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Select Arrow (1)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:arrow_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   
   new GuiBitmapButtonCtrl(ShapeEditorMoveModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "WorldEditorMove";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "28 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "GlobalGizmoProfile.mode = \"Move\";";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Move Selection (2)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:translate_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   
   new GuiBitmapButtonCtrl(ShapeEditorRotateModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "WorldEditorRotate";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "56 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "GlobalGizmoProfile.mode = \"Rotate\";";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Rotate Selection (3)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:rotate_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   
   new GuiBitmapButtonCtrl(ShapeEditorSunModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "84 0";
      Extent = "16 16";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Variable = "ShapeEdShapeView.editSun";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Rotate sun";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:sun_btn_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
};
