$paletteId = new GuiControl(ConvexEditorPalette, EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   
   new GuiBitmapButtonCtrl(ConvexEditorNoneModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Select Arrow (1)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:arrow_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      Command = "GlobalGizmoProfile.mode = \"None\";";
   };
   
   new GuiBitmapButtonCtrl(ConvexEditorMoveModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "28 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Move Selection (2)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:translate_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      Command = "GlobalGizmoProfile.mode = \"Move\";";
   };
   
   new GuiBitmapButtonCtrl(ConvexEditorRotateModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "56 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Rotate Selection (3)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:rotate_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      Command = "GlobalGizmoProfile.mode = \"Rotate\";";
   };
   
   new GuiBitmapButtonCtrl(ConvexEditorScaleModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "84 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Scale Selection (4)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:scale_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
      Command = "GlobalGizmoProfile.mode = \"Scale\";";
   };
};
