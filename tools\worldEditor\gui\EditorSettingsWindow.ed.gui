//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(EditorSettingsWindow,EditorGuiGroup) {
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   position = "0 0";
   Extent = "800 600";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";
   canSaveDynamicFields = "1";

   new GuiWindowCollapseCtrl(ESettingsWindow) {
      internalName = "EditorSettingsWindow";
      resizeWidth = "0";
      resizeHeight = "1";
      canMove = "1";
      canClose = "1";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "50 50";
      EdgeSnap = "1";
      text = "Editor Settings";
      closeCommand = "ESettingsWindow.hideDialog();";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = "30 200";
      Extent = "319 320";
      MinExtent = "319 100";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      canSaveDynamicFields = "0";

      new GuiTabBookCtrl(ESettingsWindowTabBook) {
         TabPosition = "Top";
         TabMargin = "7";
         MinTabWidth = "64";
         TabHeight = "0";
         AllowReorder = "0";
         FrontTabPadding = "0";
         Docking = "Client";
         Margin = "3 1 4 3";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         isContainer = "1";
         Profile = "ToolsGuiTabBookNoBitmapProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "201 21";
         Extent = "334 425";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";
      };
      new GuiScrollCtrl() {
         willFirstRespond = "1";
         hScrollBar = "AlwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "0";
         lockVertScroll = "0";
         constantThumbHeight = "0";
         childMargin = "0 0";
         mouseWheelScrollSpeed = "-1";
         Docking = "Left";
         Margin = "3 1 3 -1";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         isContainer = "1";
         Profile = "ToolsGuiScrollProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "1 21";
         Extent = "100 425";
         MinExtent = "100 50";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";

         new GuiTextListCtrl(ESettingsWindowList) {
            AllowMultipleSelections = "1";
            fitParentWidth = "1";
            isContainer = "0";
            Profile = "ToolsGuiListBoxProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "1 1";
            Extent = "100 2";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            canSaveDynamicFields = "0";
         };
      };
   };
};
//--- OBJECT WRITE END ---
