//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(EWTerrainPainterToolbar,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   internalName = "TerrainPainterToolbar";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "306 0";
   Extent = "800 40";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   
   new GuiTextCtrl() {
      profile = "ToolsGuiTextProfile";
      horizSizing = "right";
      vertSizing = "bottom";
      position = "6 7";
      extent = "70 16";
      minExtent = "8 8";
      visible = "1";
      text = "Brush Settings";
      maxLength = "255";
      helpTag = "0";
   };

   new GuiControl() {
      canSaveDynamicFields = "0";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiDefaultProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "760 40";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      hovertime = "1000";
      
      new GuiControl(EWTerrainPainterToolbarBrushType){
         isContainer = "1";
         profile = "ToolsGuiDefaultProfile";
         Position = "83 2";
         Extent = "94 27";
         
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "ellipse";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "0 0";
            Extent = "29 27";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "ETerrainEditor.toggleBrushType($ThisControl);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Circle Brush (V)";
            hovertime = "750";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:circleBrush_n_image";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "box";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "31 0";
            Extent = "29 27";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "ETerrainEditor.toggleBrushType($ThisControl);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Box Brush (B)";
            hovertime = "750";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:boxBrush_n_image";
         };
         
         /*
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            internalName = "selection";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiButtonProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "62 0";
            Extent = "29 27";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "ETerrainEditor.toggleBrushType($ThisControl);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Toggles the brush type.";
            hovertime = "750";
            groupNum = "0";
            buttonType = "RadioButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:maskBrush_n_image";
         };
         */
      };
      
  new GuiBitmapCtrl() {
      Enabled = "1";
      Profile = "ToolsGuiDefaultProfile";
      position = "152 3";
      Extent = "2 26";
      MinExtent = "1 1";
      bitmapAsset = "ToolsModule:separator_h_image";
   };
      
      new GuiControl(PaintBrushSizeTextEditContainer) {
         canSaveDynamicFields = "0";
         isContainer = "1";
         Profile = "ToolsGuiTransparentProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "145 5";
         Extent = "120 50";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";

         new GuiTextCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "21 5";
            Extent = "47 10";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "Size";
            maxLength = "1024";
         };
         new GuiTextEditCtrl() {
            canSaveDynamicFields = "0";
            internalName = "textEdit";
            isContainer = "0";
            HorizSizing = "right";
            VertSizing = "bottom";
            profile="ToolsGuiNumericDropSliderTextProfile";
            position = "49 2";
            Extent = "42 16";
            MinExtent = "8 16";
            canSave = "1";
            Visible = "1";
            Command = "ETerrainEditor.setBrushSize( $ThisControl.getText() );";
            validate = "TerrainPainterPlugin.validateBrushSize();";
            hovertime = "1000";
            text = "9";
            maxLength = "4";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "83 2";
            Extent = "18 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "Canvas.pushDialog(PaintBrushSizeSliderCtrlContainer);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Changes the size of the brush (CTRL + Mouse Wheel)";
            hovertime = "750";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:dropslider_n_image";
         };
      };  

      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         position = "230 3";
         Extent = "2 26";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };
                
	   new GuiControl(PaintBrushSlopeControl) {
         canSaveDynamicFields = "0";
         isContainer = "1";
         Profile = "ToolsGuiDefaultProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         Position = "245 5";
         Extent = "256 50";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";

         new GuiTextCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "21 5";
            Extent = "78 10";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Allows painting on the terrain within a specified slope";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "Slope Mask   Min";
            maxLength = "1024";
         };
         new GuiTextEditCtrl() {
            internalName = "SlopeMinAngle";
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiNumericDropSliderTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "104 2";
            Extent = "51 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            validate = "TerrainPainterPlugin.validateSlopeMinAngle();";
            Command = "ETerrainEditor.setSlopeLimitMinAngle( $ThisControl.getText() );";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Minimum terrain angle that will be paintable";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "0.0";
            maxLength = "4";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
            passwordMask = "*";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "137 2";
            Extent = "18 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Minimum terrain angle that will be paintable";
            hovertime = "1000";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:dropslider_n_image";
            Command = "Canvas.pushDialog(PaintBrushSlopeMinContainer);";
         };
         new GuiTextCtrl() {
            Profile = "ToolsGuiTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "165 5";
            Extent = "27 10";
            MinExtent = "8 2";
            text = "Max";
            tooltip = "Max terrain angle that will be paintable";
         };
         new GuiTextEditCtrl() {
            internalName = "SlopeMaxAngle";
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiNumericDropSliderTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "190 2";
            Extent = "51 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            validate = "TerrainPainterPlugin.validateSlopeMaxAngle();";
            Command = "ETerrainEditor.setSlopeLimitMaxAngle( $ThisControl.getText() );";
            tooltipprofile = "ToolsGuiToolTipProfile";
            tooltip = "Max terrain angle that will be paintable";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "90.0";
            maxLength = "4";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
            passwordMask = "*";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            isContainer = "0";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "223 2";
            Extent = "18 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            groupNum = "-1";
            tooltip = "Max terrain angle that will be paintable";
            buttonType = "PushButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:dropslider_n_image";
            Command = "Canvas.pushDialog(PaintBrushSlopeMaxContainer);";
         };
      };

      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         position = "445 3";
         Extent = "2 26";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };

      new GuiControl(PaintBrushPressureTextEditContainer,EditorGuiGroup) {
         canSaveDynamicFields = "0";
         isContainer = "1";
         Profile = "ToolsGuiTransparentProfile";
         HorizSizing = "right";
         VertSizing = "bottom";
         position = "490 5";
         Extent = "120 50";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         hovertime = "1000";

         new GuiTextCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "0 5";
            Extent = "47 10";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            hovertime = "1000";
            Margin = "0 0 0 0";
            Padding = "0 0 0 0";
            AnchorTop = "1";
            AnchorBottom = "0";
            AnchorLeft = "1";
            AnchorRight = "0";
            text = "Pressure";
            maxLength = "1024";
         };
         new GuiTextEditCtrl() {
            canSaveDynamicFields = "0";
            internalName = "textEdit";
            isContainer = "0";
            profile="ToolsGuiNumericDropSliderTextProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            position = "49 2";
            Extent = "42 16";
            MinExtent = "8 16";
            canSave = "1";
            Visible = "1";
            Command = "ETerrainEditor.setBrushPressure( ($ThisControl.getValue() / 100) );";
            hovertime = "1000";
            text = "100";
            maxLength = "3";
            historySize = "0";
            password = "0";
            tabComplete = "0";
            sinkAllKeyEvents = "0";
         };
         new GuiBitmapButtonCtrl() {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "right";
            VertSizing = "bottom";
            Position = "83 2";
            Extent = "18 18";
            MinExtent = "8 2";
            canSave = "1";
            Visible = "1";
            Command = "Canvas.pushDialog(PaintBrushPressureSliderCtrlContainer);";
            tooltipprofile = "ToolsGuiToolTipProfile";
            ToolTip = "Changes the pressure (CTRL + SHIFT + Mouse Wheel)";
            hovertime = "750";
            groupNum = "-1";
            buttonType = "PushButton";
            useMouseEvents = "0";
            bitmapAsset = "ToolsModule:dropslider_n_image";
         }; 
      };

      new GuiBitmapCtrl() {
         Enabled = "1";
         Profile = "ToolsGuiDefaultProfile";
         position = "618 3";
         Extent = "2 26";
         MinExtent = "1 1";
         bitmapAsset = "ToolsModule:separator_h_image";
      };

      new GuiControl(TerrainTextureSettingsButtonContainer,EditorGuiGroup) {
         position = "628 5";
         extent = "90 18";
         minExtent = "8 2";
         horizSizing = "right";
         vertSizing = "bottom";
         profile = "ToolsGuiTransparentProfile";
         visible = "1";
         active = "1";
         tooltipProfile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         isContainer = "1";
         canSave = "1";
         canSaveDynamicFields = "0";
         
         new GuiButtonCtrl() {
            text = "Texture Settings";
            buttonType = "pushButton";
            profile = "ToolsGuiButtonProfile";
            command = "TerrainTextureSettingsDlg.show();";
            tooltipProfile = "ToolsGuiToolTipProfile";
            position = "0 0";
            extent = "90 18";
            horizSizing = "right";
            vertSizing = "bottom";
         };
      };
   };
};
//--- OBJECT WRITE END ---

function setTerrainEditorMinSlope(%value)
{
   %val = ETerrainEditor.setSlopeLimitMinAngle( %value );
   PaintBrushSlopeControl-->SlopeMinAngle.setValue(mFloatLength( %val, 1 ));
}

function setTerrainEditorMaxSlope(%value)
{
   %val = ETerrainEditor.setSlopeLimitMaxAngle( %value );
   PaintBrushSlopeControl-->SlopeMaxAngle.setValue(mFloatLength( %val, 1 ));
}

new GuiMouseEventCtrl(PaintBrushSizeSliderCtrlContainer,EditorGuiGroup) {
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 8";
   visible = "1";
   helpTag = "0";
   class = "EditorDropdownSliderContainer";
   
   new GuiSliderCtrl() {
      canSaveDynamicFields = "0";
      internalName = "slider";
      isContainer = "0";
      Profile = "ToolsGuiSliderBoxProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = PaintBrushSizeTextEditContainer.position.x + EWTerrainPainterToolbar.position.x + 50 SPC 
         PaintBrushSizeTextEditContainer.position.y + 50;
      Extent = "112 20";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      AltCommand = "PaintBrushSizeTextEditContainer-->textEdit.setValue(mFloatLength( ($ThisControl.getValue()), 2 )); ETerrainEditor.setBrushSize( $ThisControl.value );";
      range = "1 256";
      ticks = "0";
      value = "0";
   };
}; 

new GuiMouseEventCtrl(PaintBrushSlopeMinContainer,EditorGuiGroup) {
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 8";
   visible = "1";
   helpTag = "0";
   class = "EditorDropdownSliderContainer";
   
   new GuiSliderCtrl() {
      canSaveDynamicFields = "0";
      internalName = "slider";
      isContainer = "0";
      Profile = "ToolsGuiSliderBoxProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = PaintBrushSlopeControl.position.x + EWTerrainPainterToolbar.position.x + PaintBrushSlopeControl->SlopeMinAngle.position.x - 40 SPC
         PaintBrushSlopeControl.position.y + 50;
      Extent = "112 20";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      AltCommand = "PaintBrushSlopeControl-->SlopeMinAngle.setValue(mFloatLength( ($ThisControl.getValue()), 1 )); ETerrainEditor.setSlopeLimitMinAngle(mFloatLength( ($ThisControl.getValue()), 1 ));TerrainPainterPlugin.validateSlopeMinAngle();";
      range = "0 89.9";
      ticks = "0";
      value = "0";
   };
}; 

function PaintBrushSlopeMinContainer::onWake(%this)
{
   %this-->slider.setValue(PaintBrushSlopeControl-->SlopeMinAngle.getText());
}

new GuiMouseEventCtrl(PaintBrushSlopeMaxContainer,EditorGuiGroup) {
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 8";
   visible = "1";
   helpTag = "0";
   class = "EditorDropdownSliderContainer";
   
   new GuiSliderCtrl() {
      canSaveDynamicFields = "0";
      internalName = "slider";
      isContainer = "0";
      Profile = "ToolsGuiSliderBoxProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = PaintBrushSlopeControl.position.x + EWTerrainPainterToolbar.position.x + PaintBrushSlopeControl->SlopeMaxAngle.position.x - 40 SPC
         PaintBrushSlopeControl.position.y + 50;
      Extent = "112 20";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      AltCommand = "PaintBrushSlopeControl-->SlopeMaxAngle.setValue(mFloatLength( ($ThisControl.getValue()), 1 )); ETerrainEditor.setSlopeLimitMaxAngle(mFloatLength( ($ThisControl.getValue()), 1 ));TerrainPainterPlugin.validateSlopeMaxAngle();";
      range = "0.1 90.0";
      ticks = "0";
      value = "0";
   };
}; 

function PaintBrushSlopeMaxContainer::onWake(%this)
{
   %this-->slider.setValue(PaintBrushSlopeControl-->SlopeMaxAngle.getText());
}

function PaintBrushSlopeMaxContainer::init(%this)
{
   %this-->slider.setValue("90.0");
}

new GuiMouseEventCtrl(PaintBrushPressureSliderCtrlContainer,EditorGuiGroup) {
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 8";
   visible = "1";
   helpTag = "0";
   class = "EditorDropdownSliderContainer";
   
   new GuiSliderCtrl() {
      canSaveDynamicFields = "0";
      internalName = "slider";
      isContainer = "0";
      Profile = "ToolsGuiSliderBoxProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = PaintBrushPressureTextEditContainer.position.x + EWTerrainPainterToolbar.position.x + 50 SPC 
         PaintBrushPressureTextEditContainer.position.y + 50;
      Extent = "112 20";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      AltCommand = "PaintBrushPressureTextEditContainer-->textEdit.setValue(mFloatLength( ($ThisControl.getValue()), 2 )); ETerrainEditor.setBrushPressure( $ThisControl.value );";
      range = "0 1";
      ticks = "0";
      value = "0";
   };
};
         
new GuiMouseEventCtrl(PaintBrushSoftnessSliderCtrlContainer,EditorGuiGroup) {
   horizSizing = "right";
   vertSizing = "bottom";
   position = "0 0";
   extent = "1024 768";
   minExtent = "8 8";
   visible = "1";
   helpTag = "0";
   class = "EditorDropdownSliderContainer";
   
   new GuiSliderCtrl() {
      canSaveDynamicFields = "0";
      internalName = "slider";
      isContainer = "0";
      Profile = "ToolsGuiSliderBoxProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      position = PaintBrushSoftnessTextEditContainer.position.x + EWTerrainPainterToolbar.position.x + 50 SPC 
         PaintBrushSoftnessTextEditContainer.position.y + 50;
      Extent = "112 20";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      AltCommand = "PaintBrushSoftnessTextEditContainer-->textEdit.setValue(mFloatLength( ($ThisControl.getValue()), 2 )); ETerrainEditor.setBrushSoftness( $ThisControl.value );";
      range = "0 1";
      ticks = "0";
      value = "0";
   };
};

