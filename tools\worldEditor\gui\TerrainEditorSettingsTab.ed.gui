//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(TerrainEditorSettingsTab,EditorGuiGroup) {
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   position = "0 0";
   Extent = "800 600";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";
   canSaveDynamicFields = "1";

   new GuiTabPageCtrl(ETerrainEditorSettingsPage) {
      fitBook = "1";
      text = "Terrain Editor";
      maxLength = "1024";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      isContainer = "1";
      Profile = "ToolsGuiSolidDefaultProfile";
      HorizSizing = "width";
      VertSizing = "height";
      position = "0 0";
      Extent = "208 400";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      canSaveDynamicFields = "1";

      new GuiScrollCtrl() {
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "1";
         lockVertScroll = "0";
         constantThumbHeight = "0";
         childMargin = "0 0";
         mouseWheelScrollSpeed = "-1";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         isContainer = "1";
         Profile = "ToolsGuiScrollProfile";
         HorizSizing = "width";
         VertSizing = "height";
         position = "0 0";
         Extent = "208 400";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";

         new GuiStackControl() {
            StackingType = "Vertical";
            HorizStacking = "Left to Right";
            VertStacking = "Top to Bottom";
            Padding = "0";
            isContainer = "1";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            position = "1 1";
            extent = "208 210";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";

            new GuiRolloutCtrl() {
               Profile = "GuiRolloutProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               position = "10 10";
               extent = "208 95";
               Caption = "Tool Values";
               Margin = "0 3 0 0";
               DragSizable = false;
               container = true;
                
               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  Padding = "0";
                  isContainer = "1";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "0 0";
                  Extent = "208 0";
                  MinExtent = "8 2";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000"; 
                  padding = "3";
                  
                  new GuiControl() {
                     isContainer = "1";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     extent = "208 18";
                     
                     new GuiTextCtrl() {
                        text = "Raise/Lower Height:";
                        maxLength = "1024";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        isContainer = "0";
                        Profile = "ToolsGuiTextRightProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        position = "5 1";
                        Extent = "70 16";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSaveDynamicFields = "0";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        maxLength = "1024";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        isContainer = "0";
                        Profile = "ToolsGuiNumericTextEditProfile";
                        HorizSizing = "width";
                        VertSizing = "bottom";
                        position = "81 0";
                        Extent = "121 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowTextEdit";
                        className = "ESettingsWindowTextEdit";
                           editorSettingsRead = "ETerrainEditor.adjustHeightVal = EditorSettings.value(%this.editorSettingsValue);";
                           editorSettingsValue = "TerrainEditor/ActionValues/adjustHeightVal";
                           editorSettingsWrite = "EditorGui.writeTerrainEditorSettings();";
                     };
                  };
                  new GuiControl() {
                     isContainer = "1";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     extent = "208 18";
                     
                     new GuiTextCtrl() {
                        text = "Smooth Factor:";
                        maxLength = "1024";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        isContainer = "0";
                        Profile = "ToolsGuiTextRightProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        position = "5 1";
                        Extent = "70 16";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSaveDynamicFields = "0";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        maxLength = "1024";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        isContainer = "0";
                        Profile = "ToolsGuiNumericTextEditProfile";
                        HorizSizing = "width";
                        VertSizing = "bottom";
                        position = "81 0";
                        Extent = "121 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowTextEdit";
                        className = "ESettingsWindowTextEdit";
                           editorSettingsRead = "ETerrainEditor.smoothFactor = EditorSettings.value(%this.editorSettingsValue);";
                           editorSettingsValue = "TerrainEditor/ActionValues/smoothFactor";
                           editorSettingsWrite = "EditorGui.writeTerrainEditorSettings();";
                     };
                  };
                  new GuiControl() {
                     isContainer = "1";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     extent = "208 18";
                     
                     new GuiTextCtrl() {
                        text = "Noise Factor:";
                        maxLength = "1024";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        isContainer = "0";
                        Profile = "ToolsGuiTextRightProfile";
                        HorizSizing = "right";
                        VertSizing = "bottom";
                        position = "5 1";
                        Extent = "70 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSaveDynamicFields = "0";
                     };
                     new GuiTextEditCtrl() {
                        historySize = "0";
                        password = "0";
                        tabComplete = "0";
                        sinkAllKeyEvents = "0";
                        passwordMask = "*";
                        maxLength = "1024";
                        Margin = "0 0 0 0";
                        Padding = "0 0 0 0";
                        AnchorTop = "1";
                        AnchorBottom = "0";
                        AnchorLeft = "1";
                        AnchorRight = "0";
                        isContainer = "0";
                        Profile = "ToolsGuiNumericTextEditProfile";
                        HorizSizing = "width";
                        VertSizing = "bottom";
                        position = "81 0";
                        Extent = "121 18";
                        MinExtent = "8 2";
                        canSave = "1";
                        Visible = "1";
                        tooltipprofile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowTextEdit";
                        className = "ESettingsWindowTextEdit";
                           editorSettingsRead = "ETerrainEditor.noiseFactor = EditorSettings.value(%this.editorSettingsValue);";
                           editorSettingsValue = "TerrainEditor/ActionValues/noiseFactor";
                           editorSettingsWrite = "EditorGui.writeTerrainEditorSettings();";
                     };
                  };
               };
            };
         };
      };
   };
};
//--- OBJECT WRITE END ---
