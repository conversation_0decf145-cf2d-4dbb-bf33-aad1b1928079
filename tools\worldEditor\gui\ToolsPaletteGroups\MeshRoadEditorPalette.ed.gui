$paletteId = new GuiControl(MeshRoadEditorPalette,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "1024 768";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   hovertime = "1000";
   
   new GuiBitmapButtonCtrl(EMeshRoadEditorSelectModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "MeshRoadEditorSelectMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "MeshRoadEditorGui.prepSelectionMode();";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Select Mesh Road (1)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:arrow_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   }; 
      
   new GuiBitmapButtonCtrl(EMeshRoadEditorMoveModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "MeshRoadEditorMoveMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "MeshRoadEditorGui.setMode(\"MeshRoadEditorMoveMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Move Point (2)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:move_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(EMeshRoadEditorRotateModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "MeshRoadEditorRotateMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "MeshRoadEditorGui.setMode(\"MeshRoadEditorRotateMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Rotate Point (3)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:rotate_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(EMeshRoadEditorScaleModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "MeshRoadEditorScaleMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "MeshRoadEditorGui.setMode(\"MeshRoadEditorScaleMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Scale Point (4)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:scale_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(EMeshRoadEditorAddModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "MeshRoadEditorAddRoadMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "MeshRoadEditorGui.setMode(\"MeshRoadEditorAddRoadMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Create Road (5)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:add_mesh_road_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(EMeshRoadEditorInsertModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "MeshRoadEditorInsertPointMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "MeshRoadEditorGui.setMode(\"MeshRoadEditorInsertPointMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Insert Point (+)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:add_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
   new GuiBitmapButtonCtrl(EMeshRoadEditorRemoveModeBtn) {
      canSaveDynamicFields = "0";
      internalName = "MeshRoadEditorRemovePointMode";
      Enabled = "1";
      isContainer = "0";
      Profile = "ToolsGuiButtonProfile";
      HorizSizing = "right";
      VertSizing = "bottom";
      Position = "0 0";
      Extent = "25 19";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      Command = "MeshRoadEditorGui.setMode(\"MeshRoadEditorRemovePointMode\");";
      tooltipprofile = "ToolsGuiToolTipProfile";
      ToolTip = "Remove Point (-)";
      hovertime = "1000";
      bitmapAsset = "ToolsModule:subtract_point_n_image";
      buttonType = "RadioButton";
      useMouseEvents = "0";
   };
};
