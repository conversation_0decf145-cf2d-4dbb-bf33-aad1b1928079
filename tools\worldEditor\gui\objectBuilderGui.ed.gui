//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(ObjectBuilderGui, EditorGuiGroup) {
	profile = "ToolsGuiDefaultProfile";
	horizSizing = "right";
	vertSizing = "bottom";
	position = "0 0";
	extent = "800 600";
	minExtent = "8 8";
	visible = "1";
	setFirstResponder = "0";
	modal = "1";
	helpTag = "0";

	new GuiWindowCtrl(OBTargetWindow) {
		profile = "ToolsGuiWindowProfile";
		horizSizing = "center";
		vertSizing = "center";
		position = "384 205";
		extent = "256 282";
		minExtent = "256 8";
		visible = "1";
		setFirstResponder = "0";
		modal = "1";
		helpTag = "0";
		resizeWidth = "1";
		resizeHeight = "1";
		canMove = "1";
		canClose = "0";
		canMinimize = "0";
		canMaximize = "0";
		minSize = "50 50";
		text = "Create Object";

		new GuiTextCtrl() {
			profile = "ToolsGuiTextProfile";
			horizSizing = "right";
			vertSizing = "bottom";
			position = "9 26";
			extent = "84 16";
			minExtent = "8 8";
			visible = "1";
			setFirstResponder = "0";
			modal = "1";
			helpTag = "0";
			text = "Object Name:";
		};
		new GuiTextEditCtrl(OBObjectName) {
		   class = ObjectBuilderGuiTextEditCtrl;
			profile = "ToolsGuiTextEditProfile";
			horizSizing = "width";
			vertSizing = "bottom";
			position = "78 26";
			extent = "172 18";
			minExtent = "8 8";
			visible = "1";
			setFirstResponder = "0";
			modal = "1";
			helpTag = "0";
			historySize = "0";
		};
		new GuiBitmapBorderCtrl(OBContentWindow) {
			profile = "ToolsGuiGroupBorderProfile";
			horizSizing = "width";
			vertSizing = "bottom";
			position = "7 51";
			extent = "243 193";
			minExtent = "0 0";
			visible = "1";
			setFirstResponder = "0";
			modal = "1";
			helpTag = "0";
		};
		new GuiButtonCtrl(OBOKButton) {
			profile = "ToolsGuiButtonProfile";
			horizSizing = "width";
			vertSizing = "bottom";
			position = "7 250";
			extent = "156 24";
			minExtent = "8 8";
			visible = "1";
			setFirstResponder = "0";
			modal = "1";
			command = "ObjectBuilderGui.onOK();";
			helpTag = "0";
			text = "Create New";
			Accelerator = "return";
		};
		new GuiButtonCtrl(OBCancelButton) {
			profile = "ToolsGuiButtonProfile";
			horizSizing = "left";
			vertSizing = "bottom";
			position = "170 250";
			extent = "80 24";
			minExtent = "8 8";
			visible = "1";
			setFirstResponder = "0";
			modal = "1";
			command = "ObjectBuilderGui.onCancel();";
			helpTag = "0";
			text = "Cancel";
			Accelerator = "escape";
		};
	};
};
//--- OBJECT WRITE END ---

function ObjectBuilderGui::init(%this)
{
   %this.baseOffsetX       = 5;
   %this.baseOffsetY       = 5;
   %this.defaultObjectName = "";
   %this.defaultFieldStep  = 22;
   %this.columnOffset      = 110;

   %this.fieldNameExtent   = "105 18";
   %this.textEditExtent    = "122 18";
   %this.checkBoxExtent    = "13 18";
   %this.popupMenuExtent   = "122 18";
   %this.fileButtonExtent  = "122 18";
   %this.matButtonExtent   = "17 18";

   //
   %this.numControls       = 0;
   
   %this.lastPath          = "";

   %this.reset();
}

function ObjectBuilderGui::reset(%this)
{
   %this.objectGroup       = "";
   %this.curXPos           = %this.baseOffsetX;
   %this.curYPos           = %this.baseOffsetY;
   %this.createFunction    = "";
   %this.createCallback    = "";
   %this.currentControl    = 0;

   //
   OBObjectName.setValue(%this.defaultObjectName);

   //
   %this.newObject         = 0;
   %this.objectClassName   = "";
   %this.numFields         = 0;

   //
   for(%i = 0; %i < %this.numControls; %i++)
   {
      %this.textControls[%i].delete();
      %this.controls[%i].delete();
   }
   %this.numControls = 0;
}

//------------------------------------------------------------------------------

function ObjectBuilderGui::createFileType(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::createFileType: invalid field");
      return;
   }

   //
   if(%this.field[%index, text] $= "")
      %name = %this.field[%index, name];
   else
      %name = %this.field[%index, text];

   // 
   %this.textControls[%this.numControls] = new GuiTextCtrl() {
      profile = "ToolsGuiTextRightProfile";
      text = %name;
      extent = %this.fieldNameExtent;
      position = %this.curXPos @ " " @ %this.curYPos;
      modal = "1";
   };

   // 
   %this.controls[%this.numControls] = new GuiButtonCtrl() {
      HorizSizing = "width";
      profile = "ToolsGuiButtonProfile";
      extent = %this.fileButtonExtent;
      position = %this.curXPos + %this.columnOffset @ " " @ %this.curYPos;
      modal = "1";
      command = %this @ ".getFileName(" @ %index @ ");";
   };

   %val = %this.field[%index, value];
   %this.controls[%this.numControls].setValue(fileBase(%val) @ fileExt(%val));

   %this.numControls++;
   %this.curYPos += %this.defaultFieldStep;
}

function ObjectBuilderGui::getFileName(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::getFileName: invalid field");
      return;
   }
   
   %val = %this.field[%index, ext];

   //%path = filePath(%val);
   //%ext = fileExt(%val);

   %this.currentControl = %index;
   getLoadFilename( %val @ "|" @ %val, %this @ ".gotFileName", %this.lastPath );
}

function ObjectBuilderGui::gotFileName(%this, %name)
{
   %index = %this.currentControl;
   
   %name = makeRelativePath(%name,getWorkingDirectory()); 
   
   %this.field[%index, value] = %name;
   %this.controls[%this.currentControl].setText(fileBase(%name) @ fileExt(%name));
   
   %this.lastPath = %name;
   
   // This doesn't work for button controls as getValue returns their state!
   //%this.controls[%this.currentControl].setValue(%name);
}

//------------------------------------------------------------------------------
function ObjectBuilderGui::createTerrainAssetType(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::createTerrainAssetType: invalid field");
      return;
   }

   //
   if(%this.field[%index, text] $= "")
      %name = %this.field[%index, name];
   else
      %name = %this.field[%index, text];

   // 
   %this.textControls[%this.numControls] = new GuiTextCtrl() {
      profile = "ToolsGuiTextRightProfile";
      text = %name;
      extent = %this.fieldNameExtent;
      position = %this.curXPos @ " " @ %this.curYPos;
      modal = "1";
   };

   // 
   %this.controls[%this.numControls] = new GuiButtonCtrl() {
      HorizSizing = "width";
      profile = "ToolsGuiButtonProfile";
      extent = %this.fileButtonExtent;
      position = %this.curXPos + %this.columnOffset @ " " @ %this.curYPos;
      modal = "1";
      command = %this @ ".getTerrainAsset(" @ %index @ ");";
   };

   %val = %this.field[%index, value];
   %this.controls[%this.numControls].setValue(fileBase(%val) @ fileExt(%val));

   %this.numControls++;
   %this.curYPos += %this.defaultFieldStep;
}

function ObjectBuilderGui::getTerrainAsset(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::getTerrainAsset: invalid field");
      return;
   }
   
   %val = %this.field[%index, ext];

   //%path = filePath(%val);
   //%ext = fileExt(%val);

   %this.currentControl = %index;
   AssetBrowser.showDialog("TerrainAsset", %this @ ".gotTerrainAsset", "", "", "");
   //getLoadFilename( %val @ "|" @ %val, %this @ ".gotFileName", %this.lastPath );
}

function ObjectBuilderGui::gotTerrainAsset(%this, %name)
{
   %index = %this.currentControl;
   
   %this.field[%index, value] = %name;
   %this.controls[%this.currentControl].setText(fileBase(%name) @ fileExt(%name));
   
   %this.lastPath = %name;
   
   // This doesn't work for button controls as getValue returns their state!
   //%this.controls[%this.currentControl].setValue(%name);
}
//------------------------------------------------------------------------------
function ObjectBuilderGui::createImageAssetType(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::createImageAssetType: invalid field");
      return;
   }

   //
   if(%this.field[%index, text] $= "")
      %name = %this.field[%index, name];
   else
      %name = %this.field[%index, text];

   %this.textControls[%this.numControls] = new GuiTextCtrl() {
      profile = "ToolsGuiTextRightProfile";
      text = %name;
      extent = %this.fieldNameExtent;
      position = %this.curXPos @ " " @ %this.curYPos;
      modal = "1";
   };

   // 
   %this.controls[%this.numControls] = new GuiControl() {
      HorizSizing = "width";
      profile = "ToolsGuiDefaultProfile";
      extent = %this.textEditExtent;
      position = %this.curXPos + %this.columnOffset @ " " @ %this.curYPos;
      modal = "1";
   };
   
   %text = new GuiTextEditCtrl() {
      class = ObjectBuilderGuiTextEditCtrl;
      internalName = "assetText";
      HorizSizing = "width";
      profile = "ToolsGuiTextEditProfile";
      extent = getWord(%this.textEditExtent,0) - getWord(%this.matButtonExtent,0) - 2 @ " " @ getWord(%this.textEditExtent,1);
      text = %this.field[%index, value];
      position = "0 0";
      modal = "1";
   };
   %this.controls[%this.numControls].addGuiControl(%text);
   
   %button = new GuiBitmapButtonCtrl() {
      internalName = "assetButton";
      HorizSizing = "left";
      profile = "ToolsGuiButtonProfile";
      extent = %this.matButtonExtent;
      position = getWord(%this.textEditExtent,0) - getWord(%this.matButtonExtent,0) @ " 0";
      modal = "1";
      command = %this @ ".getImageAsset(" @ %index @ ");";
   };
   %button.setBitmap("ToolsModule:GameTSCtrl_image");
   %this.controls[%this.numControls].addGuiControl(%button);

   %this.numControls++;
   %this.curYPos += %this.defaultFieldStep;
}

function ObjectBuilderGui::getImageAsset(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::getImageAsset: invalid field");
      return;
   }
   
   %val = %this.field[%index, ext];

   //%path = filePath(%val);
   //%ext = fileExt(%val);

   %this.currentControl = %index;
   AssetBrowser.showDialog("ImageAsset", %this @ ".gotImageAsset", "", "", "");
   //getLoadFilename( %val @ "|" @ %val, %this @ ".gotFileName", %this.lastPath );
}

function ObjectBuilderGui::gotImageAsset(%this, %name)
{
   %index = %this.currentControl;
   
   %this.field[%index, value] = %name;
   %this.controls[%this.currentControl]-->assetText.setText(%name);
   
   %this.lastPath = %name;
   
   // This doesn't work for button controls as getValue returns their state!
   //%this.controls[%this.currentControl].setValue(%name);
}

//------------------------------------------------------------------------------
function ObjectBuilderGui::createMaterialAssetType(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::createMaterialAssetType: invalid field");
      return;
   }

   //
   if(%this.field[%index, text] $= "")
      %name = %this.field[%index, name];
   else
      %name = %this.field[%index, text];
   
   %this.textControls[%this.numControls] = new GuiTextCtrl() {
      profile = "ToolsGuiTextRightProfile";
      text = %name;
      extent = %this.fieldNameExtent;
      position = %this.curXPos @ " " @ %this.curYPos;
      modal = "1";
   };

   // 
   %this.controls[%this.numControls] = new GuiControl() {
      HorizSizing = "width";
      profile = "ToolsGuiDefaultProfile";
      extent = %this.textEditExtent;
      position = %this.curXPos + %this.columnOffset @ " " @ %this.curYPos;
      modal = "1";
   };
   
   %text = new GuiTextEditCtrl() {
      class = ObjectBuilderGuiTextEditCtrl;
      internalName = "assetText";
      HorizSizing = "width";
      profile = "ToolsGuiTextEditProfile";
      extent = getWord(%this.textEditExtent,0) - getWord(%this.matButtonExtent,0) - 2 @ " " @ getWord(%this.textEditExtent,1);
      text = %this.field[%index, value];
      position = "0 0";
      modal = "1";
   };
   %this.controls[%this.numControls].addGuiControl(%text);
   
   %button = new GuiBitmapButtonCtrl() {
      internalName = "assetButton";
      HorizSizing = "left";
      profile = "ToolsGuiButtonProfile";
      extent = %this.matButtonExtent;
      position = getWord(%this.textEditExtent,0) - getWord(%this.matButtonExtent,0) @ " 0";
      modal = "1";
      command = %this @ ".getMaterialAsset(" @ %index @ ");";
   };
   %button.setBitmap("ToolsModule:change_material_btn_n_image");
   %this.controls[%this.numControls].addGuiControl(%button);

   %this.numControls++;
   %this.curYPos += %this.defaultFieldStep;
}

function ObjectBuilderGui::getMaterialAsset(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::getMaterialAsset: invalid field");
      return;
   }
   
   %val = %this.field[%index, ext];

   //%path = filePath(%val);
   //%ext = fileExt(%val);

   %this.currentControl = %index;
   AssetBrowser.showDialog("MaterialAsset", %this @ ".gotMaterialAsset", "", "", "");
   //getLoadFilename( %val @ "|" @ %val, %this @ ".gotFileName", %this.lastPath );
}

function ObjectBuilderGui::gotMaterialAsset(%this, %name)
{
   %index = %this.currentControl;
   
   %this.field[%index, value] = %name;
   %this.controls[%this.currentControl]-->assetText.setText(%name);
   
   %this.lastPath = %name;
   
   // This doesn't work for button controls as getValue returns their state!
   //%this.controls[%this.currentControl].setValue(%name);
}

//------------------------------------------------------------------------------
function ObjectBuilderGui::createShapeAssetType(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::createShapeAssetType: invalid field");
      return;
   }

   //
   if(%this.field[%index, text] $= "")
      %name = %this.field[%index, name];
   else
      %name = %this.field[%index, text];
   
   %this.textControls[%this.numControls] = new GuiTextCtrl() {
      profile = "ToolsGuiTextRightProfile";
      text = %name;
      extent = %this.fieldNameExtent;
      position = %this.curXPos @ " " @ %this.curYPos;
      modal = "1";
   };

   // 
   %this.controls[%this.numControls] = new GuiControl() {
      HorizSizing = "width";
      profile = "ToolsGuiDefaultProfile";
      extent = %this.textEditExtent;
      position = %this.curXPos + %this.columnOffset @ " " @ %this.curYPos;
      modal = "1";
   };
   
   %text = new GuiTextEditCtrl() {
      class = ObjectBuilderGuiTextEditCtrl;
      internalName = "assetText";
      HorizSizing = "width";
      profile = "ToolsGuiTextEditProfile";
      extent = getWord(%this.textEditExtent,0) - getWord(%this.matButtonExtent,0) - 2 @ " " @ getWord(%this.textEditExtent,1);
      text = %this.field[%index, value];
      position = "0 0";
      modal = "1";
   };
   %this.controls[%this.numControls].addGuiControl(%text);
   
   %button = new GuiBitmapButtonCtrl() {
      internalName = "assetButton";
      HorizSizing = "left";
      profile = "ToolsGuiButtonProfile";
      extent = %this.matButtonExtent;
      position = getWord(%this.textEditExtent,0) - getWord(%this.matButtonExtent,0) @ " 0";
      modal = "1";
      command = %this @ ".getShapeAsset(" @ %index @ ");";
   };
   %button.setBitmap("ToolsModule:shape_editor_n_image");
   %this.controls[%this.numControls].addGuiControl(%button);

   %this.numControls++;
   %this.curYPos += %this.defaultFieldStep;
}

function ObjectBuilderGui::getShapeAsset(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::getShapeAsset: invalid field");
      return;
   }
   
   %val = %this.field[%index, ext];

   //%path = filePath(%val);
   //%ext = fileExt(%val);

   %this.currentControl = %index;
   AssetBrowser.showDialog("ShapeAsset", %this @ ".gotShapeAsset", "", "", "");
   //getLoadFilename( %val @ "|" @ %val, %this @ ".gotFileName", %this.lastPath );
}

function ObjectBuilderGui::gotShapeAsset(%this, %name)
{
   %index = %this.currentControl;
   
   %this.field[%index, value] = %name;
   %this.controls[%this.currentControl]-->assetText.setText(%name);
   
   %this.lastPath = %name;
   
   // This doesn't work for button controls as getValue returns their state!
   //%this.controls[%this.currentControl].setValue(%name);
}

//------------------------------------------------------------------------------
function ObjectBuilderGui::createSoundAssetType(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::createSoundAssetType: invalid field");
      return;
   }

   //
   if(%this.field[%index, text] $= "")
      %name = %this.field[%index, name];
   else
      %name = %this.field[%index, text];
   
   %this.textControls[%this.numControls] = new GuiTextCtrl() {
      profile = "ToolsGuiTextRightProfile";
      text = %name;
      extent = %this.fieldNameExtent;
      position = %this.curXPos @ " " @ %this.curYPos;
      modal = "1";
   };

   // 
   %this.controls[%this.numControls] = new GuiControl() {
      HorizSizing = "width";
      profile = "ToolsGuiDefaultProfile";
      extent = %this.textEditExtent;
      position = %this.curXPos + %this.columnOffset @ " " @ %this.curYPos;
      modal = "1";
   };
   
   %text = new GuiTextEditCtrl() {
      class = ObjectBuilderGuiTextEditCtrl;
      internalName = "assetText";
      HorizSizing = "width";
      profile = "ToolsGuiTextEditProfile";
      extent = getWord(%this.textEditExtent,0) - getWord(%this.matButtonExtent,0) - 2 @ " " @ getWord(%this.textEditExtent,1);
      text = %this.field[%index, value];
      position = "0 0";
      modal = "1";
   };
   %this.controls[%this.numControls].addGuiControl(%text);
   
   %button = new GuiBitmapButtonCtrl() {
      internalName = "assetButton";
      HorizSizing = "left";
      profile = "ToolsGuiButtonProfile";
      extent = %this.matButtonExtent;
      position = getWord(%this.textEditExtent,0) - getWord(%this.matButtonExtent,0) @ " 0";
      modal = "1";
      command = %this @ ".getSoundAsset(" @ %index @ ");";
   };
   %button.setBitmap("ToolsModule:SFXEmitter_image");
   %this.controls[%this.numControls].addGuiControl(%button);

   %this.numControls++;
   %this.curYPos += %this.defaultFieldStep;
}

function ObjectBuilderGui::getSoundAsset(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::getSoundAsset: invalid field");
      return;
   }
   
   %val = %this.field[%index, ext];

   //%path = filePath(%val);
   //%ext = fileExt(%val);

   %this.currentControl = %index;
   AssetBrowser.showDialog("SoundAsset", %this @ ".gotSoundAsset", "", "", "");
   //getLoadFilename( %val @ "|" @ %val, %this @ ".gotFileName", %this.lastPath );
}

function ObjectBuilderGui::gotSoundAsset(%this, %name)
{
   %index = %this.currentControl;
   
   %this.field[%index, value] = %name;
   %this.controls[%this.currentControl]-->assetText.setText(%name);
   
   %this.lastPath = %name;
   
   // This doesn't work for button controls as getValue returns their state!
   //%this.controls[%this.currentControl].setValue(%name);
}
//------------------------------------------------------------------------------
function ObjectBuilderGui::createMaterialNameType(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::createMaterialNameType: invalid field");
      return;
   }

   //
   if(%this.field[%index, text] $= "")
      %name = %this.field[%index, name];
   else
      %name = %this.field[%index, text];

   // 
   %this.textControls[%this.numControls] = new GuiTextCtrl() {
      profile = "ToolsGuiTextRightProfile";
      text = %name;
      extent = %this.fieldNameExtent;
      position = %this.curXPos @ " " @ %this.curYPos;
      modal = "1";
   };

   // 
   %this.controls[%this.numControls] = new GuiControl() {
      HorizSizing = "width";
      profile = "ToolsGuiDefaultProfile";
      extent = %this.textEditExtent;
      position = %this.curXPos + %this.columnOffset @ " " @ %this.curYPos;
      modal = "1";
   };
   
   %text = new GuiTextEditCtrl() {
      class = ObjectBuilderGuiTextEditCtrl;
      internalName = "MatText";
      HorizSizing = "width";
      profile = "ToolsGuiTextEditProfile";
      extent = getWord(%this.textEditExtent,0) - getWord(%this.matButtonExtent,0) - 2 @ " " @ getWord(%this.textEditExtent,1);
      text = %this.field[%index, value];
      position = "0 0";
      modal = "1";
   };
   %this.controls[%this.numControls].addGuiControl(%text);
   
   %button = new GuiBitmapButtonCtrl() {
      internalName = "MatButton";
      HorizSizing = "left";
      profile = "ToolsGuiButtonProfile";
      extent = %this.matButtonExtent;
      position = getWord(%this.textEditExtent,0) - getWord(%this.matButtonExtent,0) @ " 0";
      modal = "1";
      command = %this @ ".getMaterialName(" @ %index @ ");";
   };
   %button.setBitmap("ToolsModule:change_material_btn_n_image");
   %this.controls[%this.numControls].addGuiControl(%button);

   //%val = %this.field[%index, value];
   //%this.controls[%this.numControls].setValue(%val);
   //%this.controls[%this.numControls].setBitmap("tools/materialEditor/gui/change-material-btn");

   %this.numControls++;
   %this.curYPos += %this.defaultFieldStep;
}

function ObjectBuilderGui::getMaterialName(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::getMaterialName: invalid field");
      return;
   }
   
   %this.currentControl = %index;
   materialSelector.showDialog(%this @ ".gotMaterialName", "name");
}

function ObjectBuilderGui::gotMaterialName(%this, %name)
{
   %index = %this.currentControl;
   
   %this.field[%index, value] = %name;
   %this.controls[%index]-->MatText.setText(%name);
}

//------------------------------------------------------------------------------

function ObjectBuilderGui::createDataBlockType(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::createDataBlockType: invalid field");
      return;
   }

   //
   if(%this.field[%index, text] $= "")
      %name = %this.field[%index, name];
   else
      %name = %this.field[%index, text];

   // 
   %this.textControls[%this.numControls] = new GuiTextCtrl() {
      profile = "ToolsGuiTextRightProfile";
      text = %name;
      extent = %this.fieldNameExtent;
      position = %this.curXPos @ " " @ %this.curYPos;
      modal = "1";
   };

   // 
   %this.controls[%this.numControls] = new GuiPopupMenuCtrl() {
      HorizSizing = "width";
      profile = "ToolsGuiPopUpMenuProfile";
      extent = %this.popupMenuExtent;
      position = %this.curXPos + %this.columnOffset @ " " @ %this.curYPos;
      modal = "1";
      maxPopupHeight = "200";
   };

   %classname = getWord(%this.field[%index, value], 0);
   %classname_alt = getWord(%this.field[%index, value], 1);

   %this.controls[%this.numControls].add("", -1);

   // add the datablocks
   for(%i = 0; %i < DataBlockGroup.getCount(); %i++)
   {
      %obj = DataBlockGroup.getObject(%i);
      if( isMemberOfClass( %obj.getClassName(), %classname ) || isMemberOfClass ( %obj.getClassName(), %classname_alt ) )
         %this.controls[%this.numControls].add(%obj.getName(), %i);
   }
   
   %this.controls[%this.numControls].setValue(getWord(%this.field[%index, value], 1));

   %this.numControls++;
   %this.curYPos += %this.defaultFieldStep;
}

function ObjectBuilderGui::createBoolType(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::createBoolType: invalid field");
      return;
   }
   
   //
   if(%this.field[%index, value] $= "")
      %value = 0;
   else
      %value = %this.field[%index, value];

   //
   if(%this.field[%index, text] $= "")
      %name = %this.field[%index, name];
   else
      %name = %this.field[%index, text];

   // 
   %this.textControls[%this.numControls] = new GuiTextCtrl() {
      profile = "ToolsGuiTextRightProfile";
      text = %name;
      extent = %this.fieldNameExtent;
      position = %this.curXPos @ " " @ %this.curYPos;
      modal = "1";
   };

   // 
   %this.controls[%this.numControls] = new GuiCheckBoxCtrl() {
      profile = "ToolsGuiCheckBoxProfile";
      extent = %this.checkBoxExtent;
      position = %this.curXPos + %this.columnOffset @ " " @ %this.curYPos;
      modal = "1";
   };

   %this.controls[%this.numControls].setValue(%value);
   
   %this.numControls++;
   %this.curYPos += %this.defaultFieldStep;
}

function ObjectBuilderGuiTextEditCtrl::onGainFirstResponder(%this)
{
   %this.selectAllText();
}

function ObjectBuilderGui::createStringType(%this, %index)
{
   if(%index >= %this.numFields || %this.field[%index, name] $= "")
   {
      error("ObjectBuilderGui::createStringType: invalid field");
      return;
   }
   
   //
   if(%this.field[%index, text] $= "")
      %name = %this.field[%index, name];
   else
      %name = %this.field[%index, text];

   // 
   %this.textControls[%this.numControls] = new GuiTextCtrl() {
      profile = "ToolsGuiTextRightProfile";
      text = %name;
      extent = %this.fieldNameExtent;
      position = %this.curXPos @ " " @ %this.curYPos;
      modal = "1";
   };

   // 
   %this.controls[%this.numControls] = new GuiTextEditCtrl() {
      class = ObjectBuilderGuiTextEditCtrl;
      HorizSizing = "width";
      profile = "ToolsGuiTextEditProfile";
      extent = %this.textEditExtent;
      text = %this.field[%index, value];
      position = %this.curXPos + %this.columnOffset @ " " @ %this.curYPos;
      modal = "1";
   };
   
   %this.numControls++;
   %this.curYPos += %this.defaultFieldStep;
}

//------------------------------------------------------------------------------

function ObjectBuilderGui::adjustSizes(%this)
{
   if(%this.numControls == 0)
      %this.curYPos = 0;

   OBTargetWindow.extent = getWord(OBTargetWindow.extent, 0) SPC %this.curYPos + 88;
   OBContentWindow.extent = getWord(OBContentWindow.extent, 0) SPC %this.curYPos;
   OBOKButton.position = getWord(OBOKButton.position, 0) SPC %this.curYPos + 57;
   OBCancelButton.position = getWord(OBCancelButton.position, 0) SPC %this.curYPos + 57;
}

function ObjectBuilderGui::process(%this)
{
   if(%this.objectClassName $= "")
   {
      error("ObjectBuilderGui::process: classname is not specified");
      return;
   }

   OBTargetWindow.text = "Create Object: " @ %this.objectClassName;

   //
   for(%i = 0; %i < %this.numFields; %i++)
   {
      switch$(%this.field[%i, type])
      {
         case "TypeBool":
            %this.createBoolType(%i);

         case "TypeDataBlock":
            %this.createDataBlockType(%i);

         case "TypeFile":
            %this.createFileType(%i);
            
         case "TypeTerrainAsset":
            %this.createTerrainAssetType(%i);

         case "TypeImageAsset":
            %this.createImageAssetType(%i);

         case "TypeMaterialAsset":
            %this.createMaterialAssetType(%i);
            
         case "TypeShapeAsset":
            %this.createShapeAssetType(%i);
            
         case "TypeSoundAsset":
            %this.createSoundAssetType(%i);

         case "TypeMaterialName":
            %this.createMaterialNameType(%i);

         default:
            %this.createStringType(%i);
      }
   }
   
   // add the controls   
   for(%i = 0; %i < %this.numControls; %i++)
   {
      OBContentWindow.add(%this.textControls[%i]);
      OBContentWindow.add(%this.controls[%i]);
   }

   //
   %this.adjustSizes();

   //
   Canvas.pushDialog(%this);
}

function ObjectBuilderGui::processNewObject(%this, %obj)
{
   if ( %this.createCallback !$= "" )
      eval( %this.createCallback );
   
   // Skip out if nothing was created.   
   if ( !isObject( %obj ) )
      return;
      
   // Add the object to the group.
   if( %this.objectGroup !$= "" )
      %this.objectGroup.add( %obj );
   else
      ObjectCreator.objectGroup.add( %obj );
      
   // If we were given a callback to call after the
   // object has been created, do so now.  Also clear
   // the callback to make sure it's valid only for
   // a single invocation.

   %callback = %this.newObjectCallback;
   %this.newObjectCallback = "";
   
   if( %callback !$= "" )
      eval( %callback @ "( " @ %obj @ " );" );
}

function ObjectBuilderGui::onOK(%this)
{
   // Error out if the given object name is not valid or not unique.
   %objectName = OBObjectName.getValue();
   if( !Editor::validateObjectName( %objectName, false ))
      return;

   // get current values
   for(%i = 0; %i < %this.numControls; %i++)
   {
      // uses button type where getValue returns button state!
      if (%this.field[%i, type] $= "TypeFile")
      {
         if (strchr(%this.field[%i, value],"*") !$= "")
            %this.field[%i, value] = "";
         
         continue; 
      }
      if (%this.field[%i, type] $= "TypeMaterialName")
      {
         %this.field[%i, value] = %this.controls[%i]-->MatText.getValue();
         continue;
      }
      if (%this.field[%i, type] $= "TypeImageAsset" || 
      %this.field[%i, type] $= "TypeTerrainAsset" || 
      %this.field[%i, type] $= "TypeMaterialAsset" ||
      %this.field[%i, type] $= "TypeShapeAsset"
      )
      {
         %this.field[%i, value] = %this.controls[%i]-->assetText.getText();
         continue;
      }
      %this.field[%i, value] = %this.controls[%i].getValue();
   }
      
   // If we have a special creation function then
   // let it do the construction.
   if ( %this.createFunction !$= "" )
      eval( %this.createFunction );
      
   else
   {
      // Else we use the memento.
      %memento = %this.buildMemento();
      eval( %memento );
   }
   
   if(%this.newObject != 0)
      %this.processNewObject(%this.newObject);

   %this.reset();
   Canvas.popDialog(%this);
}

function ObjectBuilderGui::onCancel(%this)
{
   %this.reset();
   Canvas.popDialog(%this);
}

function ObjectBuilderGui::addField(%this, %name, %type, %text, %value, %ext)
{
   %this.field[%this.numFields, name] = %name;
   %this.field[%this.numFields, type] = %type;
   %this.field[%this.numFields, text] = %text;
   %this.field[%this.numFields, value] = %value;
   %this.field[%this.numFields, ext] = %ext;

   %this.numFields++;
}

function ObjectBuilderGui::buildMemento(%this)
{
   // Build the object into a string.
   %this.memento = %this @ ".newObject = new " @ %this.objectClassName @ "(" @ OBObjectName.getValue() @ ") { ";         
   for( %i = 0; %i < %this.numFields; %i++ )
      %this.memento = %this.memento @ %this.field[%i, name] @ " = \"" @ %this.field[%i, value] @ "\"; ";
   %this.memento = %this.memento @ "};";
   
   return %this.memento;
}

//------------------------------------------------------------------------------
// This function is used for objects that don't require any special
// fields/functionality when being built
//------------------------------------------------------------------------------
function ObjectBuilderGui::buildObject(%this, %className)
{
   %this.objectClassName = %className;
   %this.process();
}

//------------------------------------------------------------------------------
// Environment
//------------------------------------------------------------------------------
function ObjectBuilderGui::buildSkyBox( %this)
{
   OBObjectName.setValue( "sky" );
   %this.objectClassName = "skybox";     
   %this.addField("MaterialAsset", "TypeImageAssetId", "MaterialAsset",  "Core_Rendering:BlankSkyMat"); 
   %this.process();
}
function ObjectBuilderGui::buildGroundPlane( %this )
{
   OBObjectName.setValue( "Ground" );
   %this.objectClassName = "GroundPlane";     
   %this.addField("MaterialAsset", "TypeImageAssetId", "MaterialAsset",  "Prototyping:FloorGray"); 
   %this.addField("scaleU", "TypeF32", "scaleU",  "25");
   %this.addField("scaleV", "TypeF32", "scaleV",  "25");
   %this.process();
}

function ObjectBuilderGui::buildScatterSky( %this, %dontWarnAboutSun )
{   
   if( !%dontWarnAboutSun )
   {
      // Check for sun object already in the level.  If there is one,
      // warn the user.

      initContainerTypeSearch( $TypeMasks::EnvironmentObjectType );
      while( 1 )
      {
         %object = containerSearchNext();
         if( !%object )
            break;

         if( %object.isMemberOfClass( "Sun" ) )
         {
            toolsMessageBoxYesNo( "Warning",
               "A ScatterSky object will conflict with the Sun object that is already in the level." SPC
               "Do you still want to create a ScatterSky object?",
               %this @ ".buildScatterSky( true );" );
            return;
         }
      }
   }
   
   %this.objectClassName = "ScatterSky";

   %this.addField("rayleighScattering", "TypeFloat", "Rayleigh Scattering",  "0.0035");
   %this.addField("mieScattering", "TypeFloat", "Mie Scattering", "0.0045");
   %this.addField("skyBrightness", "TypeFloat", "Sky Brightness", "25");   

   %this.process();
   
   // This is a trick... any fields added after process won't show
   // up as controls, but will be applied to the created object.
   %this.addField( "flareType", "TypeLightFlareDataPtr", "Flare", "ScatterSkyFlareExample" );
   %this.addField( "moonMatAsset", "TypeMaterialAsset", "Moon Material", "Core_Rendering:moon_wglow" );
   %this.addField( "nightCubemap", "TypeCubemapName", "Night Cubemap", "NightCubemap" );
   %this.addField( "useNightCubemap", "TypeBool", "Use Night Cubemap", "true" );
}

function ObjectBuilderGui::buildCloudLayer(%this)
{
   OBObjectName.setValue( "" );      
   %this.objectClassName = "CloudLayer";
   %this.addField( "textureAsset", "TypeImageAsset", "Image", "Core_Rendering:clouds_normal_displacement_image" ); 
	%this.process();
}

function ObjectBuilderGui::buildBasicClouds(%this)
{
   OBObjectName.setValue( "" );      
   %this.objectClassName = "BasicClouds";      
	%this.process();
	
   // This is a trick... any fields added after process won't show
   // up as controls, but will be applied to the created object.
   %this.addField( "textureAsset[0]", "TypeImageAssetId", "TextureAsset", "Core_Rendering:cloud1_image" );   
   %this.addField( "textureAsset[1]", "TypeImageAssetId", "TextureAsset", "Core_Rendering:cloud2_image" ); 
   %this.addField( "textureAsset[2]", "TypeImageAssetId", "TextureAsset", "Core_Rendering:cloud3_image" ); 

}

function ObjectBuilderGui::checkExists( %this, %classname )
{
   for ( %i = 0; %i < ObjectCreator.objectGroup.getCount(); %i++ )
   {                                                   
      %obj = ObjectCreator.objectGroup.getObject( %i );
      if ( %obj.getClassName() $= %classname ) 
         return true;
   } 
   
   return false;
}

function ObjectBuilderGui::buildsgMissionLightingFilter(%this)
{
   %this.objectClassName = "sgMissionLightingFilter";
   %this.addField("dataBlock", "TypeDataBlock", "sgMissionLightingFilter Data", "sgMissionFilterData");
   %this.process();
}

function ObjectBuilderGui::buildsgDecalProjector(%this)
{
   %this.objectClassName = "sgDecalProjector";
   %this.addField("dataBlock", "TypeDataBlock", "DecalData Data", "DecalData");
   %this.process();
}

function ObjectBuilderGui::buildsgLightObject(%this)
{
   %this.objectClassName = "sgLightObject";
   %this.addField("dataBlock", "TypeDataBlock", "LightObject Data", "sgLightObjectData");
   %this.process();
}

function ObjectBuilderGui::buildSun( %this, %dontWarnAboutScatterSky )
{
   if( !%dontWarnAboutScatterSky )
   {
      // Check for scattersky object already in the level.  If there is one,
      // warn the user.

      initContainerTypeSearch( $TypeMasks::EnvironmentObjectType );
      while( 1 )
      {
         %object = containerSearchNext();
         if( !%object )
            break;
            
         if( %object.isMemberOfClass( "ScatterSky" ) )
         {
            toolsMessageBoxYesNo( "Warning",
               "A Sun object will conflict with the ScatterSky object that is already in the level." SPC
               "Do you still want to create a Sun object?",
               %this @ ".buildSun( true );" );
            return;
         }
      }
   }

   %this.objectClassName = "Sun";

   %this.addField("direction", "TypeVector", "Direction", "1 1 -1");
   %this.addField("color", "TypeColor", "Sun color", "0.8 0.8 0.8");
   %this.addField("ambient", "TypeColor", "Ambient color", "0.2 0.2 0.2");   

   %this.process();
   
   // This is a trick... any fields added after process won't show
   // up as controls, but will be applied to the created object.
   %this.addField( "coronaMaterial", "TypeMaterialAsset", "Corona Material", "Core_Rendering:Corona_Mat" );
   %this.addField( "flareType", "TypeLightFlareDataPtr", "Flare", "SunFlareExample" );
}

function ObjectBuilderGui::buildLightning(%this)
{
   %this.objectClassName = "Lightning";

   %this.addField("dataBlock", "TypeDataBlock", "Data block", "LightningData DefaultStorm");

   %this.process();
}

function ObjectBuilderGui::addWaterObjectFields(%this)
{
   %this.addField("rippleDir[0]", "TypePoint2", "Ripple Direction", "0.000000 1.000000");
   %this.addField("rippleDir[1]", "TypePoint2", "Ripple Direction", "0.707000 0.707000");
   %this.addField("rippleDir[2]", "TypePoint2", "Ripple Direction", "0.500000 0.860000");
   %this.addField("rippleTexScale[0]", "TypePoint2", "Ripple Texture Scale", "7.140000 7.140000");
   %this.addField("rippleTexScale[1]", "TypePoint2", "Ripple Texture Scale", "6.250000 12.500000");
   %this.addField("rippleTexScale[2]", "TypePoint2", "Ripple Texture Scale", "50.000000 50.000000");
   %this.addField("rippleSpeed[0]", "TypeFloat", "Ripple Speed", "0.065");
   %this.addField("rippleSpeed[1]", "TypeFloat", "Ripple Speed", "0.09");
   %this.addField("rippleSpeed[2]", "TypeFloat", "Ripple Speed", "0.04");
   %this.addField("rippleMagnitude[0]", "TypeFloat", "Ripple Magnitude", "1.0");
   %this.addField("rippleMagnitude[1]", "TypeFloat", "Ripple Magnitude", "1.0");
   %this.addField("rippleMagnitude[2]", "TypeFloat", "Ripple Magnitude", "0.3");
   %this.addField("overallRippleMagnitude", "TypeFloat", "Overall Ripple Magnitude", "1.0");
   
   %this.addField("waveDir[0]", "TypePoint2", "Wave Direction", "0.000000 1.000000");
   %this.addField("waveDir[1]", "TypePoint2", "Wave Direction", "0.707000 0.707000");
   %this.addField("waveDir[2]", "TypePoint2", "Wave Direction", "0.500000 0.860000");
   %this.addField("waveMagnitude[0]", "TypePoint2", "Wave Magnitude", "0.2");
   %this.addField("waveMagnitude[1]", "TypePoint2", "Wave Magnitude", "0.2");
   %this.addField("waveMagnitude[2]", "TypePoint2", "Wave Magnitude", "0.2");
   %this.addField("waveSpeed[0]", "TypeFloat", "Wave Speed", "1");
   %this.addField("waveSpeed[1]", "TypeFloat", "Wave Speed", "1");
   %this.addField("waveSpeed[2]", "TypeFloat", "Wave Speed", "1");
   %this.addField("overallWaveMagnitude", "TypeFloat", "Overall Wave Magnitude", "1.0");
   
   %this.addField("rippleTex", "TypeImageAsset", "Ripple Texture", "Core_Rendering:ripple_image" );
   %this.addField("depthGradientTex", "TypeImageAsset", "Depth Gradient Texture", "Core_Rendering:depthcolor_ramp_image" );
   %this.addField("foamTex", "TypeImageAsset", "Foam Texture", "Core_Rendering:foam_image" );
}

function ObjectBuilderGui::buildWaterBlock(%this)
{
   %this.objectClassName = "WaterBlock";
   %this.addField( "baseColor", "TypeColorI", "Base Color", "45 108 171 255" );
   %this.process();
      
   // This is a trick... any fields added after process won't show
   // up as controls, but will be applied to the created object.
   %this.addWaterObjectFields();
}

function ObjectBuilderGui::buildWaterPlane(%this)
{
   %this.objectClassName = "WaterPlane";
   %this.addField( "baseColor", "TypeColorI", "Base Color", "45 108 171 255" );
   %this.process();
   
   // This is a trick... any fields added after process won't show
   // up as controls, but will be applied to the created object.
   %this.addWaterObjectFields();   
}

function ObjectBuilderGui::buildTerrainBlock(%this)
{
   /*%this.objectClassName = "TerrainBlock";
   %this.createCallback = "ETerrainEditor.attachTerrain();";

   %this.addField("terrainFile", "TypeFile", "Terrain file", "", "*.ter");
   %this.addField("terrainAsset", "TypeTerrainAsset", "Terrain Asset", "", "");
   %this.addField("squareSize", "TypeInt", "Square size", "8");

   %this.process();*/
   
   AssetBrowser.setupCreateNewAsset("TerrainAsset", AssetBrowser.selectedModule);
}

function ObjectBuilderGui::buildGroundCover( %this )
{
   %this.objectClassName = "GroundCover";
   %this.addField( "materialAsset", "TypeMaterialAsset", "Material Asset", "" );
   %this.addField( "shapeAsset[0]", "TypeShapeAsset", "Shape Asset [Optional]", "", "");
   %this.process();
   
   // This is a trick... any fields added after process won't show
   // up as controls, but will be applied to the created object.
   %this.addField( "probability[0]", "TypeFloat", "Probability", "1" );
}

function ObjectBuilderGui::buildPrecipitation(%this)
{
   %this.objectClassName = "Precipitation";
   %this.addField("dataBlock", "TypeDataBlock", "Precipitation data", "PrecipitationData");
   %this.process();
}

function ObjectBuilderGui::buildParticleEmitterNode(%this)
{
   %this.objectClassName = "ParticleEmitterNode";
   %this.addField("dataBlock", "TypeDataBlock", "datablock", "ParticleEmitterNodeData");
   %this.addField("emitter",   "TypeDataBlock", "Particle data", "ParticleEmitterData");
   %this.process();
}

function ObjectBuilderGui::buildRibbonNode(%this)
{
   %this.objectClassName = "RibbonNode";
   %this.addField("dataBlock", "TypeDataBlock", "datablock", "RibbonNodeData");
   %this.addField("ribbon",   "TypeDataBlock", "Ribbon data", "RibbonData");
   %this.process();
}

function ObjectBuilderGui::buildParticleSimulation(%this)
{
   %this.objectClassName = "ParticleSimulation";
   %this.addField("datablock", "TypeDataBlock", "datablock", "ParticleSimulationData");
	%this.process();
}

function ObjectBuilderGui::buildReflectionProbe(%this)
{
   %this.objectClassName = "ReflectionProbe";
   %this.process();
   
   %defaultPath = filePath($Server::MissionFile) @ "/" @ fileBase($Server::MissionFile) @ "/probes/";   
   %this.addField("reflectionPath", "TypeFilepath", "reflectionPath", %defaultPath);
}

function ObjectBuilderGui::buildSkylight(%this)
{
   %this.objectClassName = "Skylight";
   %this.process();
   
   %defaultPath = filePath($Server::MissionFile) @ "/" @ fileBase($Server::MissionFile) @ "/probes/";   
   %this.addField("reflectionPath", "TypeFilepath", "reflectionPath", %defaultPath);
}

//------------------------------------------------------------------------------
// Mission
//------------------------------------------------------------------------------

function ObjectBuilderGui::buildTrigger(%this)
{
   %this.objectClassName = "Trigger";
   %this.addField("dataBlock", "TypeDataBlock", "Data Block", "TriggerData defaultTrigger");
   %this.addField("polyhedron", "TypeTriggerPolyhedron", "Polyhedron", "-0.5 0.5 0.0 1.0 0.0 0.0 0.0 -1.0 0.0 0.0 0.0 1.0");
   %this.process();
}

function ObjectBuilderGui::buildPhysicalZone(%this)
{
   %this.objectClassName = "PhysicalZone";
   %this.addField("polyhedron", "TypeTriggerPolyhedron", "Polyhedron", "-0.5 0.5 0.0 1.0 0.0 0.0 0.0 -1.0 0.0 0.0 0.0 1.0");
   %this.process();
}

function ObjectBuilderGui::buildCamera(%this)
{
   %this.objectClassName = "Camera";

   %this.addField("position", "TypePoint3", "Position", "0 0 0");
   %this.addField("rotation", "TypePoint4", "Rotation", "1 0 0 0");
   %this.addField("dataBlock", "TypeDataBlock", "Data block", "CameraData Observer");
   %this.addField("team", "TypeInt", "Team", "0");

   %this.process();
}

function ObjectBuilderGui::buildLevelInfo(%this)
{
   if ( %this.checkExists( "LevelInfo" ) )
   {
      GenericPromptDialog-->GenericPromptWindow.text = "Warning";
      GenericPromptDialog-->GenericPromptText.text   = "There is already an existing LevelInfo in the scene.";
      Canvas.pushDialog( GenericPromptDialog );
      return;
   } 

   OBObjectName.setValue( "theLevelInfo" );   
   %this.objectClassName = "LevelInfo";   
   %this.process();
}

function ObjectBuilderGui::buildTimeOfDay(%this)
{
   if ( %this.checkExists( "TimeOfDay" ) )
   {
      GenericPromptDialog-->GenericPromptWindow.text = "Warning";
      GenericPromptDialog-->GenericPromptText.text   = "There is already an existing TimeOfDay in the scene.";
      Canvas.pushDialog( GenericPromptDialog );
      return;
   }
   
   %this.objectClassName = "TimeOfDay";   
   %this.process();
}

function ObjectBuilderGui::buildPlayerDropPoint(%this)
{
   %this.objectClassName = "SpawnSphere";
   %this.addField("dataBlock",    "TypeDataBlock", "dataBlock",   "MissionMarkerData SpawnSphereMarker");
   %this.addField("radius",       "TypeFloat",     "Radius",        1);
   %this.addField("sphereWeight", "TypeFloat",     "Sphere Weight", 1);

   %this.addField("spawnClass",     "TypeString",    "Spawn Class", "Player");
   %this.addField("spawnDatablock", "TypeDataBlock", "Spawn Data", "PlayerData DefaultPlayerData");

   if( ObjectCreator.objectGroup.getID() == getScene(0).getID() )
   {
      if( !isObject("PlayerDropPoints") )
         getScene(0).add( new SimGroup("PlayerDropPoints") );
      %this.objectGroup = "PlayerDropPoints";
   }

   %this.process();
}

function ObjectBuilderGui::buildObserverDropPoint(%this)
{
   %this.objectClassName = "SpawnSphere";
   %this.addField("dataBlock",    "TypeDataBlock", "dataBlock",   "MissionMarkerData SpawnSphereMarker");
   %this.addField("radius",       "TypeFloat",     "Radius",        1);
   %this.addField("sphereWeight", "TypeFloat",     "Sphere Weight", 1);

   %this.addField("spawnClass",     "TypeString",    "Spawn Class", "Camera");
   %this.addField("spawnDatablock", "TypeDataBlock", "Spawn Data", "CameraData Observer");

   if( ObjectCreator.objectGroup.getID() == getScene(0).getID() )
   {
      if( !isObject("ObserverDropPoints") )
         getScene(0).add( new SimGroup("ObserverDropPoints") );
      %this.objectGroup = "ObserverDropPoints";
   }

   %this.process();
}

function ObjectBuilderGui::buildGeneralDropPoint(%this)
{
   %this.objectClassName = "SpawnSphere";
   %this.addField("dataBlock",    "TypeDataBlock", "dataBlock",   "MissionMarkerData SpawnSphereMarker");
   %this.addField("radius",       "TypeFloat",     "Radius",        1);
   %this.addField("sphereWeight", "TypeFloat",     "Sphere Weight", 1);

   %this.addField("spawnClass",     "TypeString",    "Spawn Class", "");
   %this.addField("spawnDatablock", "TypeString", "Spawn Data", "");
   %this.addField("spawnTransform", "TypeBool", "Spawn Here", "true");
   %this.addField("canSaveDynamicFields", "TypeBool", "Save metadata", "false");
   %this.process();
}

function ObjectBuilderGui::buildNotesObject(%this)
{
   %this.objectClassName = "NotesObject";
   %this.addField("note",           "TypeString",     "Note Text", "");
   %this.addField("showArrow",      "TypeBool",       "Show Arrow", "");
   %this.addField("arrowColor",     "TypeColorI",     "Arrow Color", "255 0 0 255");
   %this.process();
}
//------------------------------------------------------------------------------
// System
//------------------------------------------------------------------------------
function ObjectBuilderGui::buildVolumetricFog(%this)
{
	// Change this if you want to default to another Folder
	// Otherwise every time you want to add a Fog you will go this.
	%defShape = "Core_Rendering:Fog_Cube";
	%this.lastPath=getMainDotCsDir() @ %defShape;
	OBObjectName.setValue( "" );
	%this.objectClassName = "VolumetricFog";
	%this.addField( "shapeAsset", "TypeShapeAsset", "Shape (Fog volume)", "", "");
	%this.addField("Scale", "TypePoint3", "Scale", "1 1 1");
	%this.addField("FogColor", "TypeColorI", "FogColor", "200 200 200 255");
	%this.process();
}
function ObjectBuilderGui::buildPhysicsEntity(%this)
{
   %this.objectClassName = "PhysicsEntity";
   %this.addField("dataBlock", "TypeDataBlock", "Data block", "PhysicsEntityData");
   %this.process();
}

//------------------------------------------------------------------------------
// Functions to allow scripted/datablock objects to be instantiated
//------------------------------------------------------------------------------

function PhysicsEntityData::create(%data)
{
   %obj = new PhysicsEntity() {
      dataBlock = %data;
      parentGroup = ObjectCreator.objectGroup;
   };
   return %obj;
}

function StaticShapeData::create(%data)
{
   %obj = new StaticShape() {
      dataBlock = %data;
      parentGroup = ObjectCreator.objectGroup;
   };
   return %obj;
}

function MissionMarkerData::create(%block)
{
   switch$(%block)
   {
      case "WayPointMarker":
         %obj = new WayPoint() {
            dataBlock = %block;
            parentGroup = ObjectCreator.objectGroup;
         };
         return(%obj);
      case "SpawnSphereMarker":
         %obj = new SpawnSphere() {
            datablock = %block;
            parentGroup = ObjectCreator.objectGroup;
         };
         return(%obj);
   }

   return(-1);
}

function ItemData::create(%data)
{
   %obj = new Item()
   {
      dataBlock = %data;
      parentGroup = ObjectCreator.objectGroup;
      static = true;
      rotate = true;
   };
   return %obj;
}

function TurretShapeData::create(%block)
{
   %obj = new TurretShape() {
      dataBlock = %block;
      static = true;
      respawn = true;
      parentGroup = ObjectCreator.objectGroup;
   };
   return %obj;
}

function AITurretShapeData::create(%block)
{
   %obj = new AITurretShape() {
      dataBlock = %block;
      static = true;
      respawn = true;
      parentGroup = ObjectCreator.objectGroup;
   };
   return %obj;
}

function WheeledVehicleData::create(%block)
{
   %obj = new WheeledVehicle() {
      dataBlock = %block;
      parentGroup = ObjectCreator.objectGroup;
   };
   return %obj;
}

function FlyingVehicleData::create(%block)
{
   %obj = new FlyingVehicle()
   {
      dataBlock = %block;
      parentGroup = ObjectCreator.objectGroup;
   };
   return(%obj);
}

function HoverVehicleData::create(%block)
{
   %obj = new HoverVehicle()
   {
      dataBlock = %block;
      parentGroup = ObjectCreator.objectGroup;
   };
   return(%obj);
}

function RigidShapeData::create(%data)
{
   %obj = new RigidShape() {
      dataBlock = %data;
      parentGroup = ObjectCreator.objectGroup;
   };
   return %obj;
}

function PhysicsShapeData::create( %datablock )
{
   %obj = new PhysicsShape()
   {
		dataBlock = %datablock;
		parentGroup = ObjectCreator.objectGroup;
		
      invulnerable = false;
      damageRadius = 0;
      areaImpulse = 0;
      radiusDamage = 0;
      minDamageAmount = 0;         
   };

   return %obj;
}

function ProximityMineData::create( %datablock )
{
   %obj = new ProximityMine()
   {
      dataBlock = %dataBlock;
      parentGroup = ObjectCreator.objectGroup;
      static = true;    // mines created by the editor are static, and armed immediately
   };

   return %obj;
}

function RibbonData::create( %datablock )
{
   %obj = new RibbonNode()
   {
      dataBlock = DefaultRibbonNodeData;
      ribbon = %datablock;
   };

   return %obj;
}

function ParticleEmitterData::create( %datablock ) 
{
   %obj = new ParticleEmitterNode()
   {
      emitter = %datablock;
      dataBlock = "DefaultEmitterNodeData";
      parentGroup = ObjectCreator.objectGroup;
   };
   return %obj;
}

function PrecipitationData::create(%datablock)
{
   %obj = new Precipitation()
   {
      dataBlock = %datablock;
      parentGroup = ObjectCreator.objectGroup;
   };
   return %obj;
}

function TriggerData::create(%datablock)
{
   %obj = new Trigger()
   {
      dataBlock = %datablock;
      parentGroup = ObjectCreator.objectGroup;
      polyhedron = "-0.5 0.5 0.0 1.0 0.0 0.0 0.0 -1.0 0.0 0.0 0.0 1.0";
   };
   return %obj;
}