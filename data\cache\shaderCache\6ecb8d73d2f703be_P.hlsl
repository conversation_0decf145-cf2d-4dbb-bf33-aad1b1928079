//*****************************************************************************
// Torque -- HLSL procedural shader
//*****************************************************************************

// Dependencies:
#include "core/rendering/shaders/torque.hlsl"

// Features:
// Vert Position
// Render Target Output = 0.0, output mask b
// Base Texture
// Diffuse Color
// Deferred Shading: PBR Config Explicit Numbers
// Deferred Shading: Mat Info Flags
// Visibility
// Eye Space Depth (Out)
// GBuffer Conditioner
// Deferred Material

struct ConnectData
{
   float4 vpos            : SV_Position;
   float2 texCoord        : TEXCOORD0;
   float4 wsEyeVec        : TEXCOORD1;
   float3 gbNormal        : TEXCOORD2;
};


struct Fragout
{
   float4 col : SV_Target0;
   float4 col1 : SV_Target1;
   float4 col2 : SV_Target2;
   float4 col3 : SV_Target3;
};


//-----------------------------------------------------------------------------
// Main
//-----------------------------------------------------------------------------
Fragout main( ConnectData IN,
              uniform SamplerState diffuseMap      : register(S0),
              uniform Texture2D diffuseMapTex   : register(T0),
              uniform float4    diffuseMaterialColor : register(C0),
              uniform float     metalness       : register(C1),
              uniform float     roughness       : register(C2),
              uniform float     matInfoFlags    : register(C3),
              uniform float     visibility      : register(C4),
              uniform float3    vEye            : register(C5),
              uniform float4    oneOverFarplane : register(C6)
)
{
   Fragout OUT;

   // Vert Position
   
   // Render Target Output = 0.0, output mask b
   OUT.col3 = 0.00001;
   
   // Base Texture
float4 diffuseColor = diffuseMapTex.Sample(diffuseMap, IN.texCoord);
   OUT.col1 = diffuseColor;
   
   // Diffuse Color
   OUT.col1 *= diffuseMaterialColor;
   
   // Deferred Shading: PBR Config Explicit Numbers
   OUT.col2.g = 1.0;
   OUT.col2.b = roughness;
   OUT.col2.a = metalness;
   
   // Deferred Shading: Mat Info Flags
   OUT.col2.r = matInfoFlags;
   
   // Visibility
   fizzle( IN.vpos.xy, visibility );
   
   // Eye Space Depth (Out)
#ifndef CUBE_SHADOW_MAP
   float eyeSpaceDepth = dot(vEye, (IN.wsEyeVec.xyz / IN.wsEyeVec.w));
#else
   float eyeSpaceDepth = length( IN.wsEyeVec.xyz / IN.wsEyeVec.w ) * oneOverFarplane.x;
#endif
   
   // GBuffer Conditioner
   float4 normal_depth = float4(normalize(IN.gbNormal), eyeSpaceDepth);

   // output buffer format: GFXFormatR16G16B16A16F
   // g-buffer conditioner: float4(normal.X, normal.Y, depth Hi, depth Lo)
   float4 _gbConditionedOutput = float4(sqrt(half(2.0/(1.0 - normal_depth.y))) * half2(normal_depth.xz), 0.0, normal_depth.a);
   
   // Encode depth into hi/lo
   float2 _tempDepth = frac(normal_depth.a * float2(1.0, 65535.0));
   _gbConditionedOutput.zw = _tempDepth.xy - _tempDepth.yy * float2(1.0/65535.0, 0.0);

   OUT.col = _gbConditionedOutput;
   
   // Deferred Material
   

   return OUT;
}
