//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(TerrainPainterContainer,EditorGuiGroup) {
   canSaveDynamicFields = "0";
   Enabled = "1";
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   Position = "0 0";
   Extent = "800 600";
   MinExtent = "8 2";
   canSave = "1";
   isDecoy = "0";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";

   new GuiWindowCollapseCtrl(EPainter) {
      canSaveDynamicFields = "0";
      internalName = "TerrainPainter";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      HorizSizing = "windowRelative";
      VertSizing = "windowRelative";
      Position = getWord($pref::Video::mode, 0) - 209 SPC getWord(EditorGuiToolbar.extent, 1)+249;
      Extent = "210 446";
      MinExtent = "210 100";
      canSave = "1";
      isDecoy = "0";
      Visible = "0";
      hovertime = "1000";
      Docking = "None";
      Margin = "4 4 4 4";
      Padding = "0 0 0 0";
      AnchorTop = "0";
      AnchorBottom = "0";
      AnchorLeft = "0";
      AnchorRight = "0";
      resizeWidth = "1";
      resizeHeight = "1";
      canMove = "1";
      canClose = "0";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "152 300";
      closeCommand = "EPainter.parentGroup.setVisible(false);";
      EdgeSnap = "1";
      text = "Terrain Painter Material Selector";
      
      new GuiScrollCtrl( EPainterScroll ) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "1";
         Profile = "ToolsGuiScrollProfile";
         HorizSizing = "width";
         VertSizing = "height";
         Position = "4 24";
         Extent = "202 418";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Docking = "Client";
         Margin = "3 1 3 3";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "true";
         lockVertScroll = "false";
         constantThumbHeight = "0";
         childMargin = "0 0";

         new GuiStackControl( EPainterStack ) {
            StackingType = "Vertical";
            HorizStacking = "Left to Right";
            VertStacking = "Top to Bottom";
            Padding = "-2";
            canSaveDynamicFields = "0";
            internalName = "theMaterialList";
            Enabled = "1";
            isContainer = "1";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            Position = "1 3";
            Extent = "200 16";
            MinExtent = "16 16";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
         };
      };
   };
   new GuiWindowCollapseCtrl(EPainterPreview) {
      canSaveDynamicFields = "0";
      internalName = "TerrainPainterPreview";
      Enabled = "1";
      isContainer = "1";
      Profile = "ToolsGuiWindowProfile";
      HorizSizing = "windowRelative";
      VertSizing = "windowRelative";
      Position = getWord($pref::Video::mode, 0) - 209 SPC getWord(EditorGuiToolbar.extent, 1)-1;
      Extent = "210 251";
      MinExtent = "210 251";
      canSave = "1";
      isDecoy = "0";
      Visible = "0";
      hovertime = "1000";
      Docking = "None";
      Margin = "4 4 4 4";
      Padding = "0 0 0 0";
      AnchorTop = "0";
      AnchorBottom = "0";
      AnchorLeft = "0";
      AnchorRight = "0";
      resizeWidth = "1";
      resizeHeight = "1";
      canMove = "1";
      canClose = "0";
      canMinimize = "0";
      canMaximize = "0";
      minSize = "152 300";
      closeCommand = "EPainter.parentGroup.setVisible(false);";
      EdgeSnap = "1";
      text = "Terrain Painter Material Preview";
      
      new GuiContainer(){
         Docking = "Client";
         Margin = "3 22 3 3";
         HorizSizing = "width";
         VertSizing = "height";
         Position = "4 24";
         Extent = "202 202";
         
         new GuiBitmapCtrl(ETerrainMaterialSelected) {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "width";
            VertSizing = "height";
            Position = "0 0";
            Extent = "202 202";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            wrap = "0";
            bitmapAsset= "ToolsModule:unknownImage_image";
         };
         new GuiBitmapCtrl(ETerrainMaterialSelectedBorder) {
            canSaveDynamicFields = "0";
            Enabled = "1";
            isContainer = "0";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "width";
            VertSizing = "height";
            Position = "0 0";
            Extent = "202 202";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            bitmapAsset = "ToolsModule:terrain_painter_border_large_image";
            wrap = "0";
         };
      };
      new GuiButtonCtrl(ETerrainMaterialSelectedEdit) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiButtonProfile";
         HorizSizing = "left";
         VertSizing = "top";
         Position = "170 229";
         Extent = "36 18";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "0";
         Command = "TerrainMaterialDlg.show(ETerrainMaterialSelected.selectedMatIndex, ETerrainMaterialSelected.selectedMat, EPainter_TerrainMaterialUpdateCallback);";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         text = "Edit";
         groupNum = "-1";
         buttonType = "PushButton";
         useMouseEvents = "0";
      };
      new GuiTextCtrl(TerrainTextureText) {
         canSaveDynamicFields = "0";
         Enabled = "1";
         isContainer = "0";
         Profile = "ToolsGuiTextProfile";
         HorizSizing = "width";
         VertSizing = "top";
         Position = "5 230";
         Extent = "162 16";
         MinExtent = "8 2";
         canSave = "1";
         isDecoy = "0";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         text = "None";
         maxLength = "1024";
      };
      new GuiButtonCtrl() {      
         canSaveDynamicFields = "0";      
         isContainer = "0";      
         Profile = "GuiButtonProfile";      
         HorizSizing = "left";      
         VertSizing = "bottom";      
         Position = "100 229";    
         Extent = "50 18";    
         MinExtent = "8 2";      
         canSave = "1";      
         Visible = "1";      
         Command = "autoLayers();";      
         tooltipprofile = "ToolsGuiToolTipProfile";
         tooltip = "Generate a layer mask for this material.";      
         hovertime = "1000";      
         text = "AutoPaint";      
         groupNum = "-1";      
         buttonType = "PushButton";      
         useMouseEvents = "0";      
      };  
   };
};
//--- OBJECT WRITE END ---
