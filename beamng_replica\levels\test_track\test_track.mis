//--- OBJECT WRITE BEGIN ---
new SimGroup(MissionGroup) {
   canSave = "1";
   canSaveDynamicFields = "1";
   
   new LevelInfo(theLevelInfo) {
      canSave = "1";
      canSaveDynamicFields = "1";
      nearPlane = "0.1";
      visibleDistance = "1000";
      decalBias = "0.0015";
      fogColor = "0.6 0.6 0.7 1";
      fogDensity = "0";
      fogDensityOffset = "700";
      fogAtmosphereHeight = "0";
      canvasClearColor = "0 0 0 255";
      ambientLightBlendPhase = "1";
      ambientLightBlendCurve = "0 0 -1 -1";
      advancedLightmapSupport = "0";
      soundAmbience = "";
      soundDistanceModel = "Linear";
   };
   
   new GroundPlane(TheGround) {
      canSave = "1";
      canSaveDynamicFields = "1";
      position = "0 0 0";
      rotation = "1 0 0 0";
      scale = "1 1 1";
      interiorFile = "";
      showTerrainInside = "0";
      squareSize = "8";
      materialList = "beamng_replica/assets/materials/ground.dml";
   };
   
   new Sun(TheSun) {
      canSave = "1";
      canSaveDynamicFields = "1";
      azimuth = "230";
      elevation = "45";
      color = "1 1 0.4 1";
      ambient = "0.1 0.1 0.1 1";
      brightness = "1";
      castShadows = "1";
      staticRefreshFreq = "250";
      dynamicRefreshFreq = "8";
      LightingModelName = "Original Stock";
      animate = "1";
      animateSpeed = "1";
      animateStartTime = "0.5";
      coronaEnabled = "1";
      coronaMaterial = "";
      coronaScale = "0.5";
      coronaTint = "1 1 1 1";
      coronaUseLightColor = "1";
      flareType = "LightFlareExample0";
      flareScale = "1";
   };
   
   new SkyBox(TheSky) {
      canSave = "1";
      canSaveDynamicFields = "1";
      position = "0 0 0";
      rotation = "1 0 0 0";
      scale = "1 1 1";
      materialList = "core/art/skies/sky_day.dml";
      drawBottom = "1";
      fogBandHeight = "0";
   };
   
   // Test vehicle spawn point
   new SpawnSphere(VehicleSpawn) {
      canSave = "1";
      canSaveDynamicFields = "1";
      position = "0 0 5";
      rotation = "0 0 1 0";
      scale = "1 1 1";
      dataBlock = "SpawnSphereMarker";
      radius = "1";
      sphereWeight = "1";
      indoorWeight = "1";
      outdoorWeight = "1";
   };
   
   // Camera spawn point
   new SpawnSphere(CameraSpawn) {
      canSave = "1";
      canSaveDynamicFields = "1";
      position = "0 -10 8";
      rotation = "0 0 1 0";
      scale = "1 1 1";
      dataBlock = "SpawnSphereMarker";
      radius = "1";
      sphereWeight = "1";
      indoorWeight = "1";
      outdoorWeight = "1";
   };
   
   // Test obstacles
   new StaticShape(TestRamp) {
      canSave = "1";
      canSaveDynamicFields = "1";
      position = "0 20 0";
      rotation = "0 0 1 0";
      scale = "10 2 1";
      dataBlock = "DefaultStaticShape";
      shapeFile = "core/art/shapes/octahedron.dts";
   };
   
   new StaticShape(TestBarrier1) {
      canSave = "1";
      canSaveDynamicFields = "1";
      position = "-15 30 0";
      rotation = "0 0 1 0";
      scale = "1 1 3";
      dataBlock = "DefaultStaticShape";
      shapeFile = "core/art/shapes/octahedron.dts";
   };
   
   new StaticShape(TestBarrier2) {
      canSave = "1";
      canSaveDynamicFields = "1";
      position = "15 30 0";
      rotation = "0 0 1 0";
      scale = "1 1 3";
      dataBlock = "DefaultStaticShape";
      shapeFile = "core/art/shapes/octahedron.dts";
   };
};
//--- OBJECT WRITE END ---
