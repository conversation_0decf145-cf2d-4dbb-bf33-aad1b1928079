# BeamNG.drive Replica - Setup Guide

This guide will help you set up and run the BeamNG.drive replica using Torque3D and JBEAM physics.

## Prerequisites

- Windows 10/11
- Torque3D Windows Binaries (already extracted)
- C++ compiler (MinGW-w64 or Visual Studio)
- Basic understanding of game development

## Project Structure

```
beamng_replica/
├── README.md              # Project overview
├── SETUP_GUIDE.md         # This file
├── build.bat              # Build script
├── assets/                # 3D models, textures, materials
│   ├── vehicles/          # Vehicle 3D models
│   ├── maps/              # Terrain and environment assets
│   └── materials/         # Material definitions (.dml files)
├── jbeam/                 # JBEAM physics definitions
│   ├── vehicles/          # Complete vehicle JBEAM files
│   │   └── sample_car.jbeam
│   └── parts/             # Individual part definitions
├── scripts/               # TorqueScript game logic
│   ├── client/            # Client-side scripts
│   ├── server/            # Server-side scripts
│   └── shared/            # Shared scripts
│       └── softBodyVehicle.tscript
├── cpp/                   # C++ engine extensions
│   ├── jbeam_parser/      # JBEAM file parser
│   │   ├── JBeamParser.h
│   │   └── JBeamParser.cpp
│   ├── physics/           # Soft-body physics implementation
│   │   ├── SoftBodyPhysics.h
│   │   └── SoftBodyPhysics.cpp
│   └── vehicle_system/    # Vehicle management system
└── levels/                # Game levels/maps
    └── test_track/        # Basic test environment
        ├── test_track.mis
        └── main.tscript
```

## Installation Steps

### 1. Build the C++ Components

Open Command Prompt in the `beamng_replica` directory and run:

```batch
build.bat
```

This will:
- Compile the JBEAM parser
- Compile the soft-body physics system
- Create a static library
- Copy scripts to Torque3D directory

### 2. Start Torque3D

1. Navigate to the main directory (where Torque3D.exe is located)
2. Run `Torque3D.exe`
3. The Torque3D console should open

### 3. Load the Test Level

In the Torque3D console, execute these commands:

```javascript
// Load the test track scripts
exec("beamng_replica/levels/test_track/main.tscript");

// Initialize the test level
initTestTrack();
```

## Controls

Once the test level is loaded:

### Vehicle Controls
- **W** - Throttle Forward
- **S** - Throttle Reverse  
- **A** - Steer Left
- **D** - Steer Right
- **Space** - Brake

### Camera Controls
- **C** - Switch between observer and vehicle camera
- **Mouse** - Look around (in observer mode)

### Debug Controls
- **R** - Reset vehicle to spawn position
- **F1** - Toggle physics debug visualization
- **F2** - Toggle wireframe rendering mode

## JBEAM Format

The JBEAM format defines vehicle physics using JSON with C-style comments:

```json
{
    "vehicle_name": {
        "information": {
            "name": "Vehicle Name",
            "authors": "Author Name"
        },
        "nodes": [
            // [id, posX, posY, posZ, {properties}]
            ["n0", -1.0, 2.0, 0.0, {"nodeWeight": 25}],
            ["n1",  1.0, 2.0, 0.0, {"nodeWeight": 25}]
        ],
        "beams": [
            // [node1, node2, {properties}]
            ["n0", "n1", {"beamSpring": 4000000, "beamDamp": 500}]
        ]
    }
}
```

### Node Properties
- `nodeWeight`: Mass of the node (default: 25)
- `collision`: Collision radius (default: 0.5)
- `frictionCoef`: Friction coefficient (default: 1.0)
- `fixed`: Whether node is fixed in space (default: false)

### Beam Properties
- `beamSpring`: Spring constant (default: 4000000)
- `beamDamp`: Damping coefficient (default: 500)
- `beamStrength`: Breaking strength (default: 1000000)
- `beamDeform`: Deformation threshold (default: 400000)

## Creating Custom Vehicles

1. **Create JBEAM File**: Add a new `.jbeam` file in `jbeam/vehicles/`
2. **Define Nodes**: Create the vehicle's structural nodes
3. **Define Beams**: Connect nodes with beams for structural integrity
4. **Test Physics**: Load and test in the test track
5. **Add 3D Model**: Create or import a 3D model for visual representation

## Troubleshooting

### Build Issues
- Ensure MinGW-w64 or Visual Studio is installed
- Check that g++ is in your PATH
- Verify all source files are present

### Runtime Issues
- Check Torque3D console for error messages
- Ensure all script files are properly copied
- Verify JBEAM file syntax is valid JSON

### Physics Issues
- Check node and beam definitions in JBEAM files
- Ensure all beam nodes reference existing node IDs
- Adjust spring and damping values for stability

## Performance Tips

1. **Optimize Node Count**: Use fewer nodes for better performance
2. **Adjust Update Rate**: Lower physics update rate if needed
3. **Limit Beam Count**: Reduce unnecessary structural beams
4. **Use Fixed Timestep**: Maintain stable physics simulation

## Next Steps

1. **Add More Vehicles**: Create additional JBEAM vehicle definitions
2. **Improve Graphics**: Add better 3D models and textures
3. **Enhanced Physics**: Implement tire physics and aerodynamics
4. **Better Levels**: Create more complex test environments
5. **UI Improvements**: Add speedometer, damage indicators, etc.

## Technical Details

### Physics System
- Uses Verlet integration for stability
- Fixed timestep at 120 Hz
- Constraint satisfaction for beam physics
- Basic collision detection with ground

### Integration Points
- C++ physics system interfaces with TorqueScript
- JBEAM parser converts JSON to physics data
- Rendering system visualizes physics nodes and beams

## Support

For issues or questions:
1. Check the console output for error messages
2. Verify JBEAM file syntax
3. Ensure all dependencies are properly installed
4. Review the sample vehicle configuration

## License

This project uses the same open-source approach as BeamNG.drive's underlying technology:
- Torque3D: MIT License
- Custom code: MIT License
- JBEAM format: Compatible with BeamNG.drive specifications
