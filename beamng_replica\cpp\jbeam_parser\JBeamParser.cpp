#include "JBeamParser.h"
#include <fstream>
#include <sstream>
#include <regex>
#include <iostream>

// JBeamVehicle helper methods
JBeamNode* JBeamVehicle::findNode(const std::string& id) {
    for (auto& node : nodes) {
        if (node.id == id) {
            return &node;
        }
    }
    return nullptr;
}

bool JBeamVehicle::validateStructure() const {
    // Check that all beam nodes exist
    for (const auto& beam : beams) {
        bool found1 = false, found2 = false;
        for (const auto& node : nodes) {
            if (node.id == beam.node1) found1 = true;
            if (node.id == beam.node2) found2 = true;
        }
        if (!found1 || !found2) {
            return false;
        }
    }
    return true;
}

// JBeamParser implementation
JBeamParser::JBeamParser() {
}

JBeamParser::~JBeamParser() {
}

std::unique_ptr<JBeamVehicle> JBeamParser::parseFile(const std::string& filePath) {
    std::ifstream file(filePath);
    if (!file.is_open()) {
        lastError = "Could not open file: " + filePath;
        return nullptr;
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    file.close();
    
    return parseString(buffer.str());
}

std::unique_ptr<JBeamVehicle> JBeamParser::parseString(const std::string& jbeamData) {
    lastError.clear();
    
    // Remove C-style comments
    std::string cleanJson = removeComments(jbeamData);
    
    auto vehicle = std::make_unique<JBeamVehicle>();
    
    // Parse different sections
    if (!parseInformation(cleanJson, *vehicle)) {
        return nullptr;
    }
    
    if (!parseNodes(cleanJson, *vehicle)) {
        return nullptr;
    }
    
    if (!parseBeams(cleanJson, *vehicle)) {
        return nullptr;
    }
    
    // Validate the parsed structure
    if (!vehicle->validateStructure()) {
        lastError = "Invalid vehicle structure: beam references non-existent nodes";
        return nullptr;
    }
    
    return vehicle;
}

std::string JBeamParser::removeComments(const std::string& jsonData) {
    std::string result = jsonData;
    
    // Remove single-line comments (// ...)
    std::regex singleLineComment(R"(//.*?$)", std::regex_constants::multiline);
    result = std::regex_replace(result, singleLineComment, "");
    
    // Remove multi-line comments (/* ... */)
    std::regex multiLineComment(R"(/\*.*?\*/)", std::regex_constants::multiline | std::regex_constants::dotall);
    result = std::regex_replace(result, multiLineComment, "");
    
    return result;
}

bool JBeamParser::parseInformation(const std::string& jsonData, JBeamVehicle& vehicle) {
    // Simple JSON parsing for information section
    std::regex infoRegex(R"("information"\s*:\s*\{([^}]*)\})");
    std::smatch match;
    
    if (std::regex_search(jsonData, match, infoRegex)) {
        std::string infoContent = match[1].str();
        
        // Parse name
        std::regex nameRegex(R"("name"\s*:\s*"([^"]*)")");
        std::smatch nameMatch;
        if (std::regex_search(infoContent, nameMatch, nameRegex)) {
            vehicle.name = nameMatch[1].str();
        }
        
        // Parse authors
        std::regex authorRegex(R"("authors"\s*:\s*"([^"]*)")");
        std::smatch authorMatch;
        if (std::regex_search(infoContent, authorMatch, authorRegex)) {
            vehicle.author = authorMatch[1].str();
        }
    }
    
    return true;
}

bool JBeamParser::parseNodes(const std::string& jsonData, JBeamVehicle& vehicle) {
    // Find nodes array
    std::regex nodesRegex(R"("nodes"\s*:\s*\[(.*?)\])", std::regex_constants::dotall);
    std::smatch match;
    
    if (!std::regex_search(jsonData, match, nodesRegex)) {
        lastError = "No nodes section found";
        return false;
    }
    
    std::string nodesContent = match[1].str();
    
    // Parse individual node arrays
    std::regex nodeRegex(R"(\[([^\]]*)\])");
    std::sregex_iterator iter(nodesContent.begin(), nodesContent.end(), nodeRegex);
    std::sregex_iterator end;
    
    for (; iter != end; ++iter) {
        std::string nodeStr = (*iter)[1].str();
        
        // Split by commas and parse node data
        std::vector<std::string> parts;
        std::stringstream ss(nodeStr);
        std::string item;
        
        while (std::getline(ss, item, ',')) {
            parts.push_back(trim(item));
        }
        
        if (parts.size() >= 4) {
            JBeamNode node;
            
            // Remove quotes from ID
            node.id = parts[0];
            if (node.id.front() == '"' && node.id.back() == '"') {
                node.id = node.id.substr(1, node.id.length() - 2);
            }
            
            node.posX = parseFloat(parts[1]);
            node.posY = parseFloat(parts[2]);
            node.posZ = parseFloat(parts[3]);
            
            // Parse optional properties object
            if (parts.size() > 4) {
                std::string propsStr = parts[4];
                if (propsStr.find("nodeWeight") != std::string::npos) {
                    std::regex weightRegex(R"("nodeWeight"\s*:\s*([0-9.]+))");
                    std::smatch weightMatch;
                    if (std::regex_search(propsStr, weightMatch, weightRegex)) {
                        node.nodeWeight = parseFloat(weightMatch[1].str(), 25.0f);
                    }
                }
            }
            
            vehicle.nodes.push_back(node);
        }
    }
    
    return true;
}

bool JBeamParser::parseBeams(const std::string& jsonData, JBeamVehicle& vehicle) {
    // Find beams array
    std::regex beamsRegex(R"("beams"\s*:\s*\[(.*?)\])", std::regex_constants::dotall);
    std::smatch match;

    if (!std::regex_search(jsonData, match, beamsRegex)) {
        lastError = "No beams section found";
        return false;
    }

    std::string beamsContent = match[1].str();

    // Parse individual beam arrays
    std::regex beamRegex(R"(\[([^\]]*)\])");
    std::sregex_iterator iter(beamsContent.begin(), beamsContent.end(), beamRegex);
    std::sregex_iterator end;

    for (; iter != end; ++iter) {
        std::string beamStr = (*iter)[1].str();

        // Split by commas and parse beam data
        std::vector<std::string> parts;
        std::stringstream ss(beamStr);
        std::string item;

        while (std::getline(ss, item, ',')) {
            parts.push_back(trim(item));
        }

        if (parts.size() >= 2) {
            JBeamBeam beam;

            // Remove quotes from node IDs
            beam.node1 = parts[0];
            if (beam.node1.front() == '"' && beam.node1.back() == '"') {
                beam.node1 = beam.node1.substr(1, beam.node1.length() - 2);
            }

            beam.node2 = parts[1];
            if (beam.node2.front() == '"' && beam.node2.back() == '"') {
                beam.node2 = beam.node2.substr(1, beam.node2.length() - 2);
            }

            // Parse optional properties object
            if (parts.size() > 2) {
                std::string propsStr = parts[2];

                // Parse beam spring
                std::regex springRegex(R"("beamSpring"\s*:\s*([0-9.]+))");
                std::smatch springMatch;
                if (std::regex_search(propsStr, springMatch, springRegex)) {
                    beam.beamSpring = parseFloat(springMatch[1].str(), 4000000.0f);
                }

                // Parse beam damping
                std::regex dampRegex(R"("beamDamp"\s*:\s*([0-9.]+))");
                std::smatch dampMatch;
                if (std::regex_search(propsStr, dampMatch, dampRegex)) {
                    beam.beamDamp = parseFloat(dampMatch[1].str(), 500.0f);
                }

                // Parse beam strength
                std::regex strengthRegex(R"("beamStrength"\s*:\s*([0-9.]+))");
                std::smatch strengthMatch;
                if (std::regex_search(propsStr, strengthMatch, strengthRegex)) {
                    beam.beamStrength = parseFloat(strengthMatch[1].str(), 1000000.0f);
                }
            }

            vehicle.beams.push_back(beam);
        }
    }

    return true;
}

// Utility methods
std::string JBeamParser::trim(const std::string& str) {
    size_t first = str.find_first_not_of(' ');
    if (std::string::npos == first) {
        return str;
    }
    size_t last = str.find_last_not_of(' ');
    return str.substr(first, (last - first + 1));
}

float JBeamParser::parseFloat(const std::string& str, float defaultValue) {
    try {
        return std::stof(str);
    } catch (const std::exception&) {
        return defaultValue;
    }
}
