//--- OBJECT WRITE BEGIN ---
$guiContent = new GuiControl(CameraSettingsTab,EditorGuiGroup) {
   isContainer = "1";
   Profile = "ToolsGuiDefaultProfile";
   HorizSizing = "right";
   VertSizing = "bottom";
   position = "0 0";
   Extent = "800 600";
   MinExtent = "8 2";
   canSave = "1";
   Visible = "1";
   tooltipprofile = "ToolsGuiToolTipProfile";
   hovertime = "1000";
   canSaveDynamicFields = "1";

   new GuiTabPageCtrl(ECameraSettingsPage) {
      fitBook = "1";
      text = "Camera Settings";
      maxLength = "1024";
      Margin = "0 0 0 0";
      Padding = "0 0 0 0";
      AnchorTop = "1";
      AnchorBottom = "0";
      AnchorLeft = "1";
      AnchorRight = "0";
      isContainer = "1";
      Profile = "ToolsGuiSolidDefaultProfile";
      HorizSizing = "width";
      VertSizing = "height";
      position = "0 0";
      Extent = "245 568";
      MinExtent = "8 2";
      canSave = "1";
      Visible = "1";
      tooltipprofile = "ToolsGuiToolTipProfile";
      hovertime = "1000";
      canSaveDynamicFields = "1";

      new GuiScrollCtrl() {
         willFirstRespond = "1";
         hScrollBar = "alwaysOff";
         vScrollBar = "dynamic";
         lockHorizScroll = "1";
         lockVertScroll = "0";
         constantThumbHeight = "0";
         childMargin = "0 0";
         mouseWheelScrollSpeed = "-1";
         Margin = "0 0 0 0";
         Padding = "0 0 0 0";
         AnchorTop = "1";
         AnchorBottom = "0";
         AnchorLeft = "1";
         AnchorRight = "0";
         isContainer = "1";
         Profile = "ToolsGuiScrollProfile";
         HorizSizing = "width";
         VertSizing = "height";
         position = "0 0";
         Extent = "245 568";
         MinExtent = "8 2";
         canSave = "1";
         Visible = "1";
         tooltipprofile = "ToolsGuiToolTipProfile";
         hovertime = "1000";
         canSaveDynamicFields = "0";
         
         new GuiStackControl() {
            internalName = "content";
            StackingType = "Vertical";
            HorizStacking = "Left to Right";
            VertStacking = "Top to Bottom";
            Padding = "0";
            isContainer = "1";
            Profile = "ToolsGuiDefaultProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            position = "1 1";
            extent = "245 210";
            MinExtent = "8 2";
            canSave = "1";
            isDecoy = "0";
            Visible = "1";
            tooltipprofile = "ToolsGuiToolTipProfile";
            hovertime = "1000";
            
            new GuiRolloutCtrl() {
               Profile = "GuiRolloutProfile";
               HorizSizing = "right";
               VertSizing = "bottom";
               position = "10 10";
               extent = "208 95";
               Caption = "Mouse Control";
               Margin = "0 0 0 -3";
               DragSizable = false;
               container = true;
                
               new GuiStackControl() {
                  StackingType = "Vertical";
                  HorizStacking = "Left to Right";
                  VertStacking = "Top to Bottom";
                  Padding = "0";
                  isContainer = "1";
                  Profile = "ToolsGuiDefaultProfile";
                  HorizSizing = "width";
                  VertSizing = "bottom";
                  position = "0 0";
                  Extent = "208 0";
                  MinExtent = "8 2";
                  canSave = "1";
                  isDecoy = "0";
                  Visible = "1";
                  tooltipprofile = "ToolsGuiToolTipProfile";
                  hovertime = "1000"; 
            
                  new GuiContainer(){
                     HorizSizing = "right";
                     VertSizing = "bottom";
                     Position = "-1 0";
                     Extent = "208 41";
                     Docking = "none";

                     new GuiCheckBoxCtrl() {
                        useInactiveState = "0";
                        text = "Invert Y Axis";
                        groupNum = "-1";
                        buttonType = "ToggleButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiCheckBoxProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "5 5";
                        extent = "140 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowCheckbox";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "Camera/invertYAxis";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                     new GuiCheckBoxCtrl() {
                        useInactiveState = "0";
                        text = "Invert X Axis";
                        groupNum = "-1";
                        buttonType = "ToggleButton";
                        useMouseEvents = "0";
                        isContainer = "0";
                        profile = "ToolsGuiCheckBoxProfile";
                        horizSizing = "right";
                        vertSizing = "bottom";
                        position = "5 22";
                        extent = "140 14";
                        minExtent = "8 2";
                        visible = "1";
                        active = "1";
                        tooltipProfile = "ToolsGuiToolTipProfile";
                        hovertime = "1000";
                        canSave = "1";
                        canSaveDynamicFields = "1";
                        class = "ESettingsWindowCheckbox";
                           editorSettingsRead = "EditorGui.readWorldEditorSettings();";
                           editorSettingsValue = "Camera/invertXAxis";
                           editorSettingsWrite = "EditorGui.writeWorldEditorSettings();";
                     };
                  };
               };
            };
         };
      };
   };
};
//--- OBJECT WRITE END ---

function ECameraSettingsPage::init(%this)
{   
   %this.currentLevel = "";
   %this.currentRolloutCtrl = "";
   
   %levelInfoPath = "LevelInformation/levels";
   for( %fieldName = EditorSettings.findFirstValue(%levelInfoPath, true, true); 
         %fieldName !$= ""; 
         %fieldName = EditorSettings.findNextValue() )
   {      
      %fieldSlashPos = 0;
      %levelSlashPos = 0;
      while( strpos( %fieldName, "/", %fieldSlashPos ) != -1 )
      {
         %levelSlashPos = %fieldSlashPos;        
         
         %temp = strpos( %fieldName, "/", %fieldSlashPos );
         %fieldSlashPos = %temp + 1;         
      }
      %levelName = getSubStr( %fieldName , %levelSlashPos , ((%fieldSlashPos - %levelSlashPos) - 1));
      
      for( %i = 0; %i < %this-->content.getCount(); %i++ )
      {
         %alreadyExist = false;
         if( %this-->content.getObject(%i).caption $= %levelName )
         {
            %alreadyExist = true;
            break;
         }
      }
      
      if( %this.currentLevel !$= %levelName && !%alreadyExist )
      {
         //Hold current level and reset gui params
         %this.currentLevel = %levelName;
         //%this.currentLevel = "\""@%levelName@"\"";
         
         //Create and hold current rollout ctrl
         %this.currentRolloutCtrl = new GuiRolloutCtrl() {
            Profile = "GuiRolloutProfile";
            HorizSizing = "width";
            VertSizing = "bottom";
            position = "10 10";
            extent = "208 95";
            Caption = %levelName;
            Margin = "0 0 0 -3";
            DragSizable = false;
            container = true;
            
            new GuiStackControl() {
               StackingType = "Vertical";
               HorizStacking = "Left to Right";
               VertStacking = "Top to Bottom";
               Padding = "0";
               isContainer = "1";
               Profile = "ToolsGuiDefaultProfile";
               HorizSizing = "width";
               VertSizing = "bottom";
               position = "0 0";
               Extent = "208 0";
               MinExtent = "8 2";
               canSave = "1";
               isDecoy = "0";
               Visible = "1";
               tooltipprofile = "ToolsGuiToolTipProfile";
               hovertime = "1000";
               
               new GuiContainer(){ //spacer
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  Position = "0 0";
                  Extent = "208 2";
               };
      
               new GuiContainer(){
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  Position = "0 0";
                  Extent = "208 22";
                  Docking = "none";
                  
                  new GuiTextCtrl() {
                     text = "Camera Speed Min:";
                     maxLength = "1024";
                     margin = "0 0 0 0";
                     padding = "0 0 0 0";
                     anchorTop = "1";
                     anchorBottom = "0";
                     anchorLeft = "1";
                     anchorRight = "0";
                     isContainer = "0";
                     profile = "ToolsGuiTextRightProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 3";
                     extent = "96 16";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "0";
                  };
                  new GuiTextEditCtrl() {
                     historySize = "0";
                     password = "0";
                     tabComplete = "0";
                     sinkAllKeyEvents = "0";
                     passwordMask = "*";
                     maxLength = "1024";
                     margin = "0 0 0 0";
                     padding = "0 0 0 0";
                     anchorTop = "1";
                     anchorBottom = "0";
                     anchorLeft = "1";
                     anchorRight = "0";
                     isContainer = "0";
                     profile = "ToolsGuiNumericTextEditProfile";
                     horizSizing = "width";
                     vertSizing = "bottom";
                     position = "106 2";
                     extent = "95 18";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowTextEdit";
                        editorSettingsRead = "EditorGui.readCameraSettings( \"" @ %levelName @ "\" );";
                        editorSettingsValue = "LevelInformation/levels/" @ %levelName @ "/cameraSpeedMin";
                        editorSettingsWrite = "EditorGui.writeCameraSettings( \"" @ %levelName @ "\" );"; // not in use
                  };
               };
               new GuiContainer(){
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  Position = "0 0";
                  Extent = "208 22";
                  Docking = "none";
                  
                  new GuiTextCtrl() {
                     text = "Camera Speed Max:";
                     maxLength = "1024";
                     margin = "0 0 0 0";
                     padding = "0 0 0 0";
                     anchorTop = "1";
                     anchorBottom = "0";
                     anchorLeft = "1";
                     anchorRight = "0";
                     isContainer = "0";
                     profile = "ToolsGuiTextRightProfile";
                     horizSizing = "right";
                     vertSizing = "bottom";
                     position = "5 3";
                     extent = "96 16";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "0";
                  };
                  new GuiTextEditCtrl() {
                     historySize = "0";
                     password = "0";
                     tabComplete = "0";
                     sinkAllKeyEvents = "0";
                     passwordMask = "*";
                     maxLength = "1024";
                     margin = "0 0 0 0";
                     padding = "0 0 0 0";
                     anchorTop = "1";
                     anchorBottom = "0";
                     anchorLeft = "1";
                     anchorRight = "0";
                     isContainer = "0";
                     profile = "ToolsGuiNumericTextEditProfile";
                     horizSizing = "width";
                     vertSizing = "bottom";
                     position = "106 2";
                     extent = "95 18";
                     minExtent = "8 2";
                     visible = "1";
                     active = "1";
                     tooltipProfile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     canSave = "1";
                     canSaveDynamicFields = "1";
                     class = "ESettingsWindowTextEdit";
                        editorSettingsRead = "EditorGui.readCameraSettings( \"" @ %levelName @ "\" );";
                        editorSettingsValue = "LevelInformation/levels/" @ %levelName @ "/cameraSpeedMax";
                        editorSettingsWrite = "EditorGui.writeCameraSettings( \"" @ %levelName @ "\" );"; // not in use
                  };
               };
               new GuiContainer(){
                  HorizSizing = "right";
                  VertSizing = "bottom";
                  Position = "0 0";
                  Extent = "208 24";
                  Docking = "none";
                  
                  new GuiButtonCtrl() {
                     canSaveDynamicFields = "0";
                     Enabled = "1";
                     isContainer = "0";
                     HorizSizing = "width";
                     VertSizing = "bottom";
                     Position = "5 2";
                     Extent = "196 18";
                     MinExtent = "8 8";
                     canSave = "1";
                     isDecoy = "0";
                     Visible = "1";
                     Command = "ECameraSettingsPage.deleteCameraSettingsGroup(\"" @ %levelName @ "\", $ThisControl.getParent().getParent().getParent());";
                     tooltipprofile = "ToolsGuiToolTipProfile";
                     hovertime = "1000";
                     text = "Delete Level Settings";
                     groupNum = "-1";
                     buttonType = "PushButton";
                     useMouseEvents = "1";
                  };
               };
            };
         };
         %this-->content.add(%this.currentRolloutCtrl);
      }
   }
}

function ECameraSettingsPage::deleteCameraSettingsGroup( %this, %levelName, %rolloutCtrl )
{
   if( %levelName $= EditorGui.levelName )
   {
      toolsMessageBoxOK("Error", "You may not delete the settings group associated with the currently loaded level");
      return;
   }
   
   %levelInfoPath = "LevelInformation/levels/" @ %levelName;
   for( %fieldName = EditorSettings.findFirstValue(%levelInfoPath, true, true); 
         %fieldName !$= ""; 
         %fieldName = EditorSettings.findNextValue() )
   {
      EditorSettings.remove( %fieldName, true );
   }
   
   %rolloutCtrl.delete();
}
